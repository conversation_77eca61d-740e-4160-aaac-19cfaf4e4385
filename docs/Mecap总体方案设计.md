# Mecap 方案设计说明书
> screenshots
> 一键将截图转换为 PDF 的功能，支持多页合并(screen capture as PDF)

## 项目目标
- **截图功能**：
  - PC 端（Windows、macOS）：支持屏幕截图、窗口截图、区域截图，包含编辑功能（如标注、裁剪）。
  - 移动端（Android、iOS）：支持屏幕截图，适配移动端 UI 和权限。
  - 数据同步：截图可在 PC 和移动端之间通过云服务实时同步。
  - 截图内容识别：识别内容，分类存贮
  - 截图内容搜索
  - 截图管理：自动添加语义标签，根据内容打标签分类

- **技术要求**：
  - 使用 Tauri 实现跨平台（PC 和移动端）。
  - 前端使用 Vite + React + TypeScript，确保高效开发和类型安全。
  - 后端使用 Rust（Tauri 后端）处理截图和文件操作。
  - 云存储和同步通过 Firebase 或 AWS S3 实现。
  - pnpm 作为包管理工具。

### 状态管理模块设计

#### 设计目标
实现统一状态管理模型，协调智能窗口检测、编辑模式和工具栏位置状态，解决以下问题：
- 状态分散在多处（前端Zustand store + Tauri后端）
- 跨组件通信效率低下
- 状态持久化需求
- 窗口检测状态同步延迟

#### 状态模型
```mermaid
classDiagram
    class EditorState {
        +currentTool: ToolType
        +isEditing: boolean
        +canvasSize: { width, height }
        +zoom: number
        +shapes: Shape[]
        +selectedShapeId: string|null
        +history: HistoryState[]
        +historyIndex: number
        +cropArea: CropArea|null
        +windowState: WindowState
        +toolbarState: ToolbarState
    }

    class WindowState {
        +detectionMode: "hover"|"select"|"none"
        +selectedWindow: WindowRect|null
        +hoveredWindow: WindowRect|null
    }

    class ToolbarState {
        +position: "top"|"bottom"|"left"|"right"
        +isVisible: boolean
        +quickActions: string[]
    }

    EditorState "1" *-- "1" WindowState
    EditorState "1" *-- "1" ToolbarState
```

#### 状态更新机制
**跨组件通信方案**
- **Zustand选择器**：组件按需订阅状态片段
  ```ts
  // 仅订阅工具栏位置
  const position = useEditorStore(state => state.toolbarState.position);
  ```
- **Tauri事件总线**：前后端状态同步
  ```rust
  // Rust端发送状态更新
  window.emit("state-update", payload).unwrap();
  ```

**状态持久化策略**
```ts
// 持久化配置
const useEditorStore = create<EditorState>()(
  persist(
    (set, get) => ({ /* ... */ }),
    {
      name: "editor-storage",
      partialize: (state) => ({ 
        toolbarState: state.toolbarState,
        windowState: { detectionMode: state.windowState.detectionMode }
      })
    }
  )
);
```

#### 性能优化
| 策略         | 实现方式                          | 预期收益               |
|--------------|-----------------------------------|----------------------|
| 状态分片     | 隔离高频状态(windowState)         | 减少无关组件重渲染    |
| 批量更新     | Zustand set合并多个状态变更       | 单次渲染完成多重更新  |
| 记忆化选择器 | shallow比较避免引用变更           | 阻止不必要渲染        |

#### Tauri集成扩展
新增Rust API端点：
```rust
// 获取编辑器状态
#[tauri::command]
async fn get_editor_state() -> Result<EditorState, String> {
    // 从共享状态获取
}

// 更新窗口状态
#[tauri::command]
async fn update_window_state(state: WindowState) -> Result<(), String> {
    // 更新后广播到所有窗口
}
```

#### 实施计划
```mermaid
gantt
    title 状态管理模块开发计划
    dateFormat  YYYY-MM-DD
    section 前端改造
    状态模型重构     :active,  des1, 2025-07-15, 1d
    持久化集成       :         des2, after des1, 1d
    性能优化实现     :         des3, after des2, 1d

    section Tauri集成
    状态同步API      :         des4, 2025-07-15, 1d
    事件通信机制     :         des5, after des4, 1d

    section 测试验证
    单元测试        :         des6, after des5, 1d
    性能测试        :         des7, after des6, 1d
```

- **性能与体验**：
  - 快速的开发和构建流程（Vite）。
  - 一致的跨平台 UI（React + Material-UI）。
  - 高效的同步机制（WebSocket 或 HTTP ？）。

- **未来展望**：
  - **高级编辑和标注工具**：内置文字、形状、箭头等标注功能，支持裁剪、模糊敏感信息。
  - **自定义选项**：支持自定义快捷键、捕获区域和默认保存路径。
  - **无缝分享**：支持直接发送到 Slack、Discord、邮件，或生成分享链接。
  - **跨平台一致性**：确保工具在 Windows、macOS、Linux、Android、iOS 上功能一致。
  - **性能优化**：开发轻量级工具，减少资源占用，支持快速捕获。
  - **定期更新**：确保工具兼容最新操作系统，持续添加新功能。
  - **免费或低成本**：提供免费版本包含基本功能，付费版本提供高级功能。
  - **隐私保护**：内置敏感信息模糊功能，确保云存储安全。
  - **组织功能**：支持标签、搜索和文件夹管理，帮助用户整理截图。
  - **屏幕录制支持**：为需要录制视频的用户提供一体化解决方案。
  - **简洁界面**：设计直观界面，减少学习曲线，同时为高级用户提供可选功能。
  - **工作流整合**：支持与笔记应用（如 Evernote）、项目管理工具（如 Trello）整合。
  - 自动美化、比如渐变色背景，圆角、投影、选不同的窗口样式，导出高清ß

---

## L1/L2架构设计
### L1
技术栈选择
| 组件          | 技术选择                | 理由                                                                 |
|---------------|-------------------------|----------------------------------------------------------------------|
| **框架**      | Tauri 2.6              | 跨平台支持（PC 和移动端），轻量打包，插件系统支持截图和同步功能。    |
| **前端**      | React + TypeScript + Vite | React 提供动态 UI，TypeScript 增强类型安全，Vite 提供快速开发体验。  |
| **后端**      | Rust (Tauri 后端)       | Tauri 内置 Rust 后端，高性能，适合处理截图和文件操作。               |
| **存储**      | Firebase Storage        | 易于集成，支持文件存储和实时同步，适合中小型项目。                   |
| **同步**      | Firebase Realtime Database / WebSocket | 提供实时数据同步，简化跨设备通信。                                  |
| **UI 库**     | Material-UI | 提供跨平台一致的 UI 组件，适配 PC 和移动端。                        |

### L2
### L2 级别架构设计：使用 Vite + React + TypeScript + Tauri 开发跨平台截图软件与手机同步

**L2 级别架构设计**聚焦于高层次的系统结构，定义主要组件、模块交互和开发阶段，而不过分深入实现细节。本方案基于 Tauri 2.6、Vite、React 和 TypeScript，针对跨平台（PC 和移动端）截图软件及其手机同步功能，涵盖前端、后端、存储和同步的设计，并明确 UI 设计所在的阶段。

#### 架构概述
- **目标**：
  - 开发一款跨平台截图软件，支持 PC（Windows、macOS、Linux）和移动端（Android、iOS）。
  - 实现PC端截图功能（屏幕、窗口、区域）和基本编辑（标注、裁剪），移动端实现屏幕截图功能。
  - 支持截图在设备间通过云服务同步。
- **架构原则**：
  - **跨平台一致性**：确保 PC 和移动端功能和体验统一。
  - **模块化**：前后端分离，模块松耦合，便于扩展。
  - **高效开发**：利用 Vite 和 React 加速开发，TypeScript 确保代码健壮性。
  - **安全性**：保护截图数据，遵守平台权限要求。

**架构图**（简化的 L2 级别模块交互）：
```
[User]
   |
[Frontend: React + TypeScript + Vite]
   ├── UI Layer: (Material-UI)
   │   ├── Screenshot Preview
   │   ├── Editor Canvas
   │   └── Sync Status
   ├── State Management: (Zustand)
   └── API Client: (Tauri IPC, Firebase SDK)
   |
[Backend: Tauri (Rust)]
   ├── Screenshot Module: (tauri-plugin-screenshots, Custom Plugins)
   ├── File Module: (tauri-plugin-filesystem)
   └── Sync Module: (HTTP/WebSocket to Firebase)
   |
[Storage & Sync]
   ├── Local Storage: (Device Filesystem)
   └── Cloud Storage: (Firebase Storage + Realtime Database)
```

#### 前端模块
- **技术栈**：React 18, TypeScript, Vite, Material-UI
- **模块**：
  1. **UI 层**：
     - 负责截图预览、编辑和同步状态显示。
     - 使用 Material-UI 提供响应式组件，适配 PC 和移动端。
  2. **状态管理**：
     - 使用 Zustand 管理截图列表、同步状态和设置。
     - 示例状态：`{ screenshots: string[], syncStatus: 'idle' | 'syncing' | 'success' | 'error' }`
  3. **API 客户端**：
     - 通过 `@tauri-apps/api` 调用 Tauri 后端命令（截图、文件操作）。
     - 使用 Firebase SDK 实现云存储和同步。
- **职责**：
  - 提供交互式界面，展示截图和同步状态。
  - 调用后端 API 执行截图和文件操作。
  - 管理与云服务的通信。

#### 后端模块
- **技术栈**：Tauri (Rust), Plugins
- **模块**：
  1. **截图模块**：
     - PC 端：使用 `tauri-plugin-screenshots` 捕获屏幕/窗口。
     - 移动端：开发自定义插件，调用 Android MediaProjection 和 iOS Swift API。
  2. **文件模块**：
     - 使用 `tauri-plugin-filesystem` 保存截图到本地（如 `~/screenshots`）。
     - 提供文件读写接口给前端。
  3. **同步模块**：
     - 使用 `tauri-plugin-http` 或 `reqwest` 上传截图到 Firebase Storage。
     - 支持 WebSocket 或 Firebase Realtime Database 同步元数据。
- **职责**：
  - 处理截图捕获和本地存储。
  - 提供 IPC 接口供前端调用。
  - 管理云服务通信和权限。

#### 存储与同步
- **本地存储**：
  - 使用设备文件系统存储截图（PC 和移动端）。
  - 路径示例：PC（`~/screenshots`），Android（`/sdcard/screenshots`）。
- **云存储**：
  - 使用 **Firebase Storage** 存储截图文件。
  - 目录结构：`screenshots/{userId}/{timestamp}.png`。
- **同步机制**：
  - 使用 **Firebase Realtime Database** 存储截图元数据（如路径、时间戳、设备）。
  - 流程：
    1. 设备保存截图到本地。
    2. 上传截图到 Firebase Storage。
    3. 更新 Realtime Database 元数据。
    4. 其他设备监听数据库变化，下载新截图。
- **通知**：
  - 使用 `tauri-plugin-notification` 在同步完成时推送通知。

#### 同步机制的鲁棒性：
    离线支持和冲突解决的设计
    - 离线队列：当设备离线时，新的截图或编辑操作应加入本地队列，待网络恢复后自动同步。
    - 数据一致性：虽然 Firebase Realtime Database
    能处理大部分情况，但仍需考虑极端情况下的数据冲突（例如，两台设备同时修改同一张截图的标签）。可以采用“最后写入者获胜”策略或更复杂的合并逻辑。

#### 平台原生体验：
    虽然 Material-UI 提供了优秀的一致性，但在 PC 端，用户更习惯原生应用的体验。例如：
    - macOS：应用菜单栏（Menu Bar）的集成。
    - Windows：系统托盘（System Tray）的右键菜单功能。
    Tauri 提供了与这些系统特性交互的能力，善用它们能让应用感觉更“原生”、更专业。

### 4. 数据流与交互
- **截图流程**：
  1. 用户触发截图（PC：快捷键，移动端：按钮）。
  2. 前端调用 Tauri 后端（`invoke('take_screenshot')`）。
  3. 后端返回截图路径，前端显示预览。
  4. 用户编辑截图（标注、裁剪），保存到本地。
- **同步流程**：
  1. 截图保存后，前端调用 Tauri 后端上传至 Firebase Storage。
  2. 更新 Firebase Realtime Database 元数据。
  3. 其他设备监听数据库变化，下载新截图。
  4. 显示同步状态和通知。
- **交互示例**：
  ```
  User -> Frontend (React) -> Tauri Backend (Rust) -> Firebase Storage
  Firebase Realtime Database -> Other Devices
  ```

**建议**：
1. 启动需求分析，优先设计 UI 原型（Figma）。
2. 使用 `create-tauri-app` 初始化项目，快速进入开发。
3. 开发 PC 端截图功能后，扩展移动端插件和同步逻辑。
4. 定期参考 [Tauri GitHub Releases](https://github.com/tauri-apps/tauri/releases) 跟踪更新。

**支持资源**：
- [Tauri 文档](https://v2.tauri.app/)
- [Vite 文档](https://vitejs.dev/)
- [React 文档](https://react.dev/)
- [Firebase 文档](https://firebase.google.com/docs)
- [Material-UI 文档](https://mui.com/)
- [Tauri GitHub Releases](https://github.com/tauri-apps/tauri/releases)
