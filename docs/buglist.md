## 待解决问题
后续新增代码都使用Tauri 的log插件接口记录日志。
devtools控制台日志在 @devtools_console.log vs终端日志在 @vs_terminal.log，请读取
问题一：
    智能窗口截图完成后，系统应立即进入编辑模式，同时显示快捷操作工具栏。在用户选择具体的编辑操作（如标注、裁剪等）之前，鼠标应处于禁用状态，不允许进行任何拖拽或绘制操作，以确保用户操作的准确性和流程的规范性。

问题二：
    全局快捷键'Cmd+Shift+C'在应用非激活状态下无法触发截图。需要：
    1. 检查context7 Tauri的全局快捷键注册配置是否正确设置了system-wide属性
    2. 验证快捷键注册时机是否合适（建议在应用启动时就完成注册）
    3. 确保快捷键注册使用了正确的权限声明
    4. 添加快捷键注册状态的日志，便于问题定位

