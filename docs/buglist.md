## 待解决问题
**问题描述：**
1、智能窗口检测模式时，当窗口不是top层，被其他窗口遮挡部分区域是，当前的 async fn capture_region_new 无法正确截取窗口的实际内容，而是包含了部分其他窗口内容。
2、区域选择模式时，当用户拖拽选择区域时，没有任何反应，期望是出现一个选择区域，该区域可以调整大小和位置。

**期望：**
我们的提供两种功能：
    1、智能窗口检测模式，此时要使用xcap的 Window::capture_image 接口来捕获窗口截图
    2、区域选择模式，此时要使用xcap的 Monitor::capture_region 接口来捕获区域截图，通过用户拖拽来确定待截图的区域，当用户没有拖拽选择区域时默认是全屏截图

问题二描述：
进入智能窗口截图模式后，窗口检测和高亮正常，单击窗口后没有截图成功，之后再移动鼠标，又进入了窗口检测模式

我测试了最新版本，窗口截图还是失败。请分析vs终端日志和devtools控制台日志。
你的解决方案是通过标题匹配的方式来找需要截图的窗口，这个方案有两个问题：1、性能问题 2、标题可能不匹配问题或多个窗口同名时错误识别的问题。我觉得有两个方案请你分析哪个更合适：方案1、鼠标在窗口上移动时已经做了窗口检测，此时可以在后端把窗口id带回给前端缓存，当用点击高亮窗口时前端再把缓存的窗口id窗给后端，后端xcap根据窗口id直接调用接口截图
