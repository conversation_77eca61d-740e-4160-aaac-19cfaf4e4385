# 截图流程方案详细设计

## 一、触发方式 (Triggering Methods)
1.  **菜单栏触发**: 在Mecap的窗口菜单（Window Menu）中添加“Capture”选项，下拉菜单中有’Capture screen‘，点击后启动截图流程。
2.  **全局快捷键**: 设置一个可配置的全局键盘快捷键（例如 `Cmd+Shift+C`），让用户可以从系统的任何地方快速启动截图，提高效率。
3.  **系统托盘**: 在系统托盘区域显示Mecap图标，提供以下功能：
    * **快速访问**: 右键点击托盘图标显示上下文菜单
    * **菜单选项**:
        - 开始截图：快速启动截图功能
        - 打开主界面：显示Mecap主窗口
        - 设置：打开设置界面
        - 查看历史：打开截图历史记录
        - 退出：完全退出应用程序
    * **状态显示**:
        - 托盘图标随应用状态变化（如截图中显示不同图标）
        - 鼠标悬停时显示当前状态提示

## 二、截图流程 (Capture Workflow)

1.  **启动与全屏捕捉**:
    *   触发截图后，Mecap应用主窗口首先需要隐藏自身
    *   隐藏后，立即进入截图预览模式

2.  **截图编辑状态**:
    * **状态转换**: 截图完成后自动切换至编辑状态
    * **核心功能**:
        - 标注工具: 支持箭头/矩形/椭圆/直线/虚线/马赛克/文字/序号/撤销/固定/区域录制/关闭/确认等图形标注
        - 文本编辑: 可添加文字注释
        - 区域裁剪: 提供矩形裁剪功能
        - 图像处理: 支持马赛克/模糊等效果
    * **交互特性**:
        - 实时预览编辑效果
        - 支持多步撤销/重做

3.  **智能窗口检测模式 (Window Detection)**:
    *   **输入条件**:
        - 用户鼠标在截图浮层上移动
    *   **系统行为**:
        1. 实时检测当前鼠标位置下最顶层的窗口对象
        2. 提供视觉反馈：
            - 高亮显示：彩色边框标识窗口轮廓
            - 元信息展示：窗口右上角悬浮显示
                * 窗口标题(Window.title)
                * 应用图标(Window.icon)
    *   **用户交互响应**:
        - 点击窗口后触发状态转换：
            1. 执行操作：
                - 自动捕获窗口截图
                - 进入编辑模式(背景变暗)
            2. 模式说明：
                - 窗口选择：自动边界识别
                - 区域选择：手动绘制
        - 拖动行为：
            - 快捷操作栏同步移动(绑定关系)

4.  **区域选择模式 (Region Selection)**:
    *   **交互方式**:
        - 用户通过左键点击并拖拽在浮层上绘制矩形选区
        - 实时显示选区坐标信息(x,y,width,height)
    *   **模式特性**:
        - 作为核心截图方式，提供最大灵活性
        - 选区完成后自动触发状态转换：
            1. 视觉反馈：
                - 选区高亮显示
                - 非选区区域变暗处理
            2. 功能激活：
                - 弹出快捷操作工具栏
                - 进入可编辑状态
    *   **选区调整**:
        - 在未执行编辑操作前允许：
            - 位置调整：拖动选区改变位置
            - 尺寸调整：拖拽边缘/角点改变大小
        - 工具栏联动：随选区移动保持相对位置不变

5.  **小型快捷操作栏**:
    *   当用户完成窗口或区域的选择后，弹出一个小型的快捷操作栏。
    *   提供“矩形”、“椭圆”、“文字”、“画笔”、“箭头”、“序号”、“直线”、“虚线”、“马赛克”、“固定”、“撤销”、“区域录制”、“关闭”、“确认”的编辑方式，方便用户进行快速操作。 

6. **区域录制模式**：
    * **状态定义**:
        - 初始状态: 用户选定录制区域
        - 准备状态: 显示录制区域虚线边框
        - 录制状态: 正在录制中
        - 暂停状态: 暂时停止录制
        - 完成状态: 录制结束等待保存

    * **录制控制界面**:
        - 位置: 录制区域顶部固定悬浮
        - 组件:
            1. 时间计数器: 显示当前录制时长
            2. 控制按钮组:
                - 开始/暂停按钮
                - 继续按钮(暂停时显示)
                - 停止按钮
            3. 录制状态指示器
        
    * **数据处理流程**:
        1. 帧捕获:
            - 固定帧率(如30fps)捕获选定区域
            - 使用内存缓冲区存储帧数据
        2. 编码处理:
            - 实时编码为临时视频流
            - 支持可配置的编码参数
        3. 导出选项:
            - 视频格式: MP4(H.264编码)
            - GIF格式: 可选择质量和帧率
            
    * **视觉反馈**:
        - 录制区域: 虚线动画边框
        - 状态指示: 录制时显示红点动画
        - 时间显示: 格式化为 MM:SS

## 三、编辑功能 (Editing Features)
1.  **进入编辑**: 用户在选择截图区域或窗口，并在快捷操作栏点击任意按钮后可进入编辑模式，进入编辑模式后截图局域不再能变化。
2.  **增强的编辑工具**: 提供一套强大的编辑工具集：
    *   **矩形、椭圆、箭头、直线、虚线**: 用于绘制图形。
    *   **文字**: 支持添加文字注释。
    *   **画笔**: 自由绘制工具。
    *   **马赛克**: 用于遮盖模糊敏感信息。
    *   **序号**: 方便地为步骤或要点添加数字标记。
    *   **撤销**: 提供撤销和重做功能，最多支持50次操作历史记录。
    *   **固定**: 把已经编辑图片固定到屏幕最上方，快捷操作栏消失，结束编辑。被固定的图片可以被拖动，但不能被编辑。
    *   **区域录制**: 选择后进入’区域录制模式‘。
    *   **关闭**: 退出截图操作，放弃之前的任何编辑。
    *   **确认**: 保存编辑后的截图到本地，拷贝一份到剪贴板。

## 四、保存与管理 (Saving & Management)

1.  **保存操作**:
    *   **自动保存**: 
        - 每次截图完成后自动保存一个原始版本
        - 编辑过程中定期自动保存(每30秒)，防止意外丢失
    *   **手动保存**:
        - 在编辑界面点击"确认"按钮或按回车键时保存
        - 选择"固定到屏幕"时保存
    *   **保存内容**:
        - 保存最终截图到本地文件系统
        - 同时复制到系统剪贴板
        - 可选保存编辑历史状态，支持后续再编辑
    *   **保存格式**:
        - 默认PNG格式保存，保持透明度
        - 提供JPG格式选项，减小文件体积
    *   **保存路径**:
        - 默认保存至用户配置的指定目录
        - 支持自定义文件名格式(时间戳/序号等)
        - 可选择是否按日期自动创建子文件夹
2.  **截图管理器**:
    *   在Mecap的菜单栏中提供一个“管理截图”的入口。
    *   点击后打开Mecap的主界面，该界面用于展示和管理所有历史截图。

## Mecap退出
Mecap菜单添加’退出‘按钮: 点击后退出截图流程，退出Mecap进程

Console
Terminal output
Browser