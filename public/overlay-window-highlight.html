<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Window Highlight</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent !important;
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到子元素 */
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到事件捕获层 */
        }

        .window-highlight {
            position: absolute;
            border: 3px solid #FF6B35;
            background: rgba(255, 107, 53, 0.15);
            border-radius: 6px;
            display: none;
            pointer-events: none;
            transition: all 0.08s ease-out;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.3),
                0 0 20px rgba(255, 107, 53, 0.6),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            z-index: 1000;
            animation: highlightPulse 2s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0%, 100% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.3),
                    0 0 20px rgba(255, 107, 53, 0.6),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.5),
                    0 0 30px rgba(255, 107, 53, 0.8),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
            }
        }

        .window-info {
            position: absolute;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 13px;
            display: none;
            pointer-events: none;
            max-width: 350px;
            min-width: 200px;
            z-index: 1001;
            border: 1px solid rgba(255, 107, 53, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .window-info .title {
            font-weight: 600;
            color: #FF6B35;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.2;
            max-width: 100%;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .window-info .app-name {
            color: #bbb;
            font-size: 12px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .window-info .info-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .window-info .app-icon {
            font-size: 24px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 107, 53, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 53, 0.2);
            flex-shrink: 0;
        }

        .window-info .info-text {
            flex: 1;
            min-width: 0;
        }

        .window-info .dimensions {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 11px;
            color: #888;
            background: rgba(255, 255, 255, 0.05);
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .instructions {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            pointer-events: auto;
        }

        .instructions h3 {
            margin-bottom: 8px;
            color: #FF6B35;
        }

        .mode-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 107, 53, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
            pointer-events: auto;
        }

        .detection-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 11px;
            min-width: 200px;
            pointer-events: auto;
        }

        .detection-stats .label {
            color: #FF6B35;
            font-weight: bold;
        }

        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 999;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 107, 53, 0.8);
        }

        .crosshair::before {
            width: 100vw;
            height: 1px;
            top: 0;
            left: -50vw;
        }

        .crosshair::after {
            width: 1px;
            height: 100vh;
            top: -50vh;
            left: 0;
        }

        /* 透明事件捕获层 */
        .event-capture-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            pointer-events: auto;
            z-index: 2000; /* 🔧 CRITICAL FIX: 确保事件捕获层在所有其他元素之上 */
        }

        /* 编辑模式相关样式 */
        .dimming-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1001;
            display: none;
            pointer-events: none;
        }

        .dimming-overlay.active {
            display: block;
        }

        .editing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1003;
            display: none;
        }

        .editing-canvas.active {
            display: block;
            pointer-events: auto;
        }

        .screenshot-container {
            position: absolute;
            border: 3px solid #4CAF50;
            background: transparent;
            border-radius: 6px;
            box-shadow:
                0 0 0 1px rgba(76, 175, 80, 0.3),
                0 0 20px rgba(76, 175, 80, 0.6);
            z-index: 1004;
            display: none;
        }

        .screenshot-container.active {
            display: block;
        }

        .screenshot-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <!-- 🔧 DEBUG: 添加可视化调试信息 -->
        <div id="debugInfo" style="position: fixed; top: 10px; left: 10px; background: red; color: white; padding: 10px; z-index: 9999; font-size: 14px;">
            DEBUG: JavaScript NOT LOADED
        </div>

        <!-- 透明事件捕获层 -->
        <div class="event-capture-layer" id="eventCaptureLayer"></div>

        <!-- 背景变暗层 -->
        <div class="dimming-overlay" id="dimmingOverlay"></div>

        <!-- 编辑模式Canvas -->
        <canvas class="editing-canvas" id="editingCanvas"></canvas>

        <!-- 截图容器 -->
        <div class="screenshot-container" id="screenshotContainer">
            <img id="screenshotImage" alt="Selected window screenshot">
        </div>

        <div class="instructions">
            <h3>🎯 Smart Window Detection</h3>
            <p>Move mouse to highlight windows (with smart filtering)</p>
            <p>Click to select | <strong>ESC</strong> to exit</p>
        </div>

        <div class="mode-indicator" id="modeIndicator">
            WINDOW DETECT
        </div>

        <div class="window-highlight" id="windowHighlight"></div>
        <div class="window-info" id="windowInfo">
            <div class="info-header">
                <div class="app-icon" id="appIcon">🪟</div>
                <div class="info-text">
                    <div class="title">Window Title</div>
                    <div class="app-name">Application Name</div>
                </div>
            </div>
            <div class="dimensions">Position: (0, 0) | Size: 0×0</div>
        </div>

        <div class="crosshair" id="crosshair"></div>

        <div class="detection-stats">
            <div><span class="label">Mouse:</span> <span id="mousePos">(0, 0)</span></div>
            <div><span class="label">Windows:</span> <span id="windowCount">0</span></div>
            <div><span class="label">Current:</span> <span id="currentWindow">None</span></div>
            <div><span class="label">Detection:</span> <span id="detectionTime">0ms</span></div>
            <div><span class="label">API:</span> <span id="apiUsed">Smart</span></div>
            <div><span class="label">Process:</span> <span id="processName">-</span></div>
        </div>
    </div>

    <script>
        // 🔧 CRITICAL DEBUG: 最简单的测试
        console.log('[FRONTEND] 🚀 CRITICAL: JavaScript is executing!');
        console.log('[FRONTEND] 🚀 DOM ready state:', document.readyState);
        console.log('[FRONTEND] 🚀 Tauri API available:', typeof window.__TAURI__ !== 'undefined');

        // 🔧 CRITICAL FIX: 确保在DOM完全加载后再执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeOverlay);
        } else {
            initializeOverlay();
        }

        function initializeOverlay() {
            console.log('[FRONTEND] 🚀 Initializing overlay after DOM ready');

            try {
                // 🔧 CRITICAL FIX: 等待Tauri API加载
                if (typeof window.__TAURI__ === 'undefined') {
                    console.log('[FRONTEND] 🚀 Waiting for Tauri API...');
                    setTimeout(initializeOverlay, 100);
                    return;
                }

                console.log('[FRONTEND] 🚀 Tauri API loaded, setting up event listeners');
                setupEventListeners();

            } catch (e) {
                console.error('Initialization failed:', e);
            }
        }

        function setupEventListeners() {
            console.log('[FRONTEND] 🚀 Setting up event listeners');

            // 🔧 CRITICAL FIX: 简化的事件监听器设置
            const eventCaptureLayer = document.getElementById('eventCaptureLayer');
            if (eventCaptureLayer) {
                console.log('[FRONTEND] 🚀 Event capture layer found, adding listeners');

                // 直接在事件捕获层上添加点击监听器
                eventCaptureLayer.addEventListener('click', function(e) {
                    console.log('[FRONTEND] 🚨 DIRECT CLICK on event capture layer!');
                    console.log('[FRONTEND] 🚨 Click at:', e.clientX, e.clientY);
                    handleClick(e);
                }, { passive: false });

                console.log('[FRONTEND] 🚀 Event listeners added successfully');
            } else {
                console.error('[FRONTEND] ❌ Event capture layer not found!');
            }
        }

        // 🔧 CRITICAL DEBUG: 立即更新可视化调试信息
        function updateDebugInfo(message, color = 'green') {
            const debugInfo = document.getElementById('debugInfo');
            if (debugInfo) {
                debugInfo.textContent = 'DEBUG: ' + message;
                debugInfo.style.background = color;
            }
        }

        // 立即执行调试更新
        updateDebugInfo('JavaScript STARTED!', 'blue');

        console.log('[FRONTEND] 🚀 JavaScript execution started');
        console.log('[FRONTEND] 🚀 Document ready state:', document.readyState);

        // 立即测试基础功能
        console.log('[FRONTEND] 🧪 Testing basic functionality...');
        console.log('[FRONTEND] 🧪 Window object available:', !!window);
        console.log('[FRONTEND] 🧪 Document object available:', !!document);
        console.log('[FRONTEND] 🧪 Tauri API available:', !!window.__TAURI__);

        // 应用状态管理
        const AppState = {
            DETECTING: 'detecting',
            WINDOW_SELECTED: 'selected',
            CAPTURING: 'capturing',
            EDITING: 'editing',
            ERROR: 'error'
        };

        const appStateManager = {
            currentState: AppState.DETECTING,
            selectedWindow: null,
            screenshotData: null,
            editingRegion: null,
            errorTimeout: null, // 用于保存错误恢复的定时器ID

            setState(newState, data = {}) {
                console.log(`[STATE] ${this.currentState} → ${newState}`);

                // 如果存在正在执行的错误恢复定时器，则清除它
                if (this.errorTimeout) {
                    clearTimeout(this.errorTimeout);
                    this.errorTimeout = null;
                }

                const oldState = this.currentState;
                this.currentState = newState;
                Object.assign(this, data);
                this.onStateChange(newState, oldState);

                // 关键修复：如果新状态是ERROR，则在短暂显示错误信息后自动恢复
                if (newState === AppState.ERROR) {
                    console.error("[STATE] An error occurred. Reverting to detection mode in 2 seconds.");
                    this.errorTimeout = setTimeout(() => {
                        this.setState(AppState.DETECTING, {
                            selectedWindow: null,
                            screenshotData: null,
                            editingRegion: null
                        });
                    }, 2000);
                }
            },

            onStateChange(newState, oldState) {
                this.updateUI(newState);
                this.updateEventHandlers(newState, oldState);
            },

            updateUI(state) {
                const modeIndicator = document.getElementById('modeIndicator');
                const dimmingOverlay = document.getElementById('dimmingOverlay');
                const editingCanvas = document.getElementById('editingCanvas');
                const screenshotContainer = document.getElementById('screenshotContainer');
                const crosshair = document.getElementById('crosshair');
                const instructions = document.querySelector('.instructions');

                // 关键修复：先将所有UI元素重置为默认状态
                dimmingOverlay.classList.remove('active');
                screenshotContainer.classList.remove('active');
                editingCanvas.classList.remove('active');
                crosshair.style.display = 'none';
                instructions.innerHTML = `<h3>🎯 Smart Window Detection</h3><p>Move mouse to highlight windows (with smart filtering)</p><p>Click to select | <strong>ESC</strong> to exit</p>`;

                // 根据当前状态精确设置UI
                switch (state) {
                    case AppState.DETECTING:
                        modeIndicator.textContent = 'WINDOW DETECT';
                        crosshair.style.display = 'block';
                        break;
                    case AppState.CAPTURING:
                        modeIndicator.textContent = 'CAPTURING...';
                        break;
                    case AppState.EDITING:
                        modeIndicator.textContent = 'EDITING MODE';
                        dimmingOverlay.classList.add('active');
                        screenshotContainer.classList.add('active');
                        editingCanvas.classList.add('active');
                        break;
                    case AppState.ERROR:
                        modeIndicator.textContent = 'ERROR';
                        instructions.innerHTML = `<h3 style="color: red;">Capture Failed</h3><p>Please try again. Reverting automatically...</p>`;
                        crosshair.style.display = 'none'; // 确保错误状态下隐藏十字准星
                        break;
                }
            },

            updateEventHandlers(newState, oldState) {
                // 根据新状态管理窗口检测
                if (newState === AppState.DETECTING) {
                    this.enableWindowDetection();
                } else {
                    this.disableWindowDetection();
                }

                // 根据新状态管理编辑模式
                if (newState === AppState.EDITING) {
                    this.enableEditingMode();
                } else {
                    this.disableEditingMode();
                }
            },

            disableWindowDetection() {
                detectionEnabled = false;
                hideHighlight();
            },

            enableWindowDetection() {
                detectionEnabled = true;
            },

            enableEditingMode() {
                console.log('[STATE] Editing mode enabled');
            },

            disableEditingMode() {
                console.log('[STATE] Editing mode disabled');
            }
        };

        // 测试状态管理器
        console.log('[FRONTEND] 🧪 State manager initialized:', !!appStateManager);
        console.log('[FRONTEND] 🧪 Initial state:', appStateManager.currentState);
        console.log('[FRONTEND] 🧪 AppState constants:', AppState);

        // 🔧 CRITICAL FIX: Alternative click handling using Tauri WebView API
        console.log('[FRONTEND] 🔧 Setting up alternative Tauri-based click handling...');

        // Check if we can use Tauri's WebView window API for events
        if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
            console.log('[FRONTEND] 🔧 Tauri WebView API available, setting up window-level event handling');

            try {
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                console.log('[FRONTEND] 🔧 Current window obtained:', !!currentWindow);

                // Listen for window-level events
                currentWindow.listen('tauri://click', (event) => {
                    console.log('[FRONTEND] 🔧 Tauri window click event:', event);
                });

            } catch (error) {
                console.error('[FRONTEND] 🔧 Failed to set up Tauri window events:', error);
            }
        } else {
            console.log('[FRONTEND] 🔧 Tauri WebView API not available, using DOM events only');
        }

        let currentWindowId = null;
        let windows = [];
        let detectionEnabled = true;
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;

        // 窗口选择模式相关变量
        let isWindowSelected = false;
        let selectedWindowId = null;
        let quickToolbar = null;
        let dimOverlay = null;

        const windowHighlight = document.getElementById('windowHighlight');
        const windowInfo = document.getElementById('windowInfo');
        const crosshair = document.getElementById('crosshair');
        const mousePos = document.getElementById('mousePos');
        const windowCount = document.getElementById('windowCount');
        const currentWindowElement = document.getElementById('currentWindow');
        const detectionTime = document.getElementById('detectionTime');
        const apiUsed = document.getElementById('apiUsed');
        const processName = document.getElementById('processName');
        const eventCaptureLayer = document.getElementById('eventCaptureLayer');

        // 强化事件监听 - 绑定到事件捕获层
        console.log('[FRONTEND] 🎯 Setting up event listeners on capture layer...');
        console.log('[FRONTEND] 🎯 Event capture layer element:', eventCaptureLayer);
        console.log('[FRONTEND] 🎯 Event capture layer style:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🔧 CRITICAL FIX: 验证pointer-events层级修复
        console.log('[FRONTEND] 🔧 Verifying pointer-events hierarchy fix:');
        console.log('[FRONTEND] 🔧   - body pointer-events:', window.getComputedStyle(document.body).pointerEvents);
        console.log('[FRONTEND] 🔧   - overlay-container pointer-events:', window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents);
        console.log('[FRONTEND] 🔧   - event-capture-layer pointer-events:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🚨 CRITICAL TEST: Verify our CSS fixes are applied
        const bodyPointerEvents = window.getComputedStyle(document.body).pointerEvents;
        const containerPointerEvents = window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents;
        const layerPointerEvents = window.getComputedStyle(eventCaptureLayer).pointerEvents;

        if (bodyPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: body still has pointer-events: none!');
        }
        if (containerPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: overlay-container still has pointer-events: none!');
        }
        if (layerPointerEvents !== 'auto') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: event-capture-layer does not have pointer-events: auto!');
        }

        if (bodyPointerEvents === 'auto' && containerPointerEvents === 'auto' && layerPointerEvents === 'auto') {
            console.log('[FRONTEND] ✅ All pointer-events settings are correct!');
        }

        eventCaptureLayer.addEventListener('mousemove', handleMouseMove, { passive: false });
        eventCaptureLayer.addEventListener('click', handleClick, { passive: false });
        eventCaptureLayer.addEventListener('mousedown', handleMouseDown, { passive: false });
        eventCaptureLayer.addEventListener('mouseup', handleMouseUp, { passive: false });

        // 🔧 REMOVED: 删除重复的点击监听器，避免与handleClick冲突

        // 添加document级别的点击测试
        document.addEventListener('click', function(e) {
            console.log('[FRONTEND] 🧪 DOCUMENT CLICK TEST - Click detected on document!');
            console.log('[FRONTEND] 🧪 Click coordinates:', e.clientX, e.clientY);
            console.log('[FRONTEND] 🧪 Target element:', e.target);

            // 🔧 CRITICAL DEBUG: 可视化文档点击反馈
            updateDebugInfo('DOC CLICK! Target: ' + e.target.tagName, 'red');
        }, { passive: false });

        // 🔧 CRITICAL FIX: Test cursor events availability
        console.log('[FRONTEND] 🔧 Testing cursor events availability...');

        // Test if we can receive basic mouse events
        let mouseEventCount = 0;
        document.addEventListener('mousemove', function(e) {
            mouseEventCount++;
            if (mouseEventCount % 50 === 0) { // Log every 50th event for more frequent feedback
                console.log('[FRONTEND] 🔧 Mouse events working - count:', mouseEventCount, 'at', e.clientX, e.clientY);
            }
        }, { passive: true });

        // 🔧 CRITICAL TEST: Add immediate click test on document
        document.addEventListener('click', function(e) {
            console.log('[FRONTEND] 🚨 DOCUMENT CLICK DETECTED!');
            console.log('[FRONTEND] 🚨 Click at:', e.clientX, e.clientY);
            console.log('[FRONTEND] 🚨 Target:', e.target.tagName, e.target.className);
            console.log('[FRONTEND] 🚨 Event phase:', e.eventPhase);
            console.log('[FRONTEND] 🚨 Bubbles:', e.bubbles);
        }, { passive: false, capture: true }); // Use capture phase to catch early

        // Test pointer events
        document.addEventListener('pointerdown', function(e) {
            console.log('[FRONTEND] 🔧 POINTER DOWN detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        document.addEventListener('pointerup', function(e) {
            console.log('[FRONTEND] 🔧 POINTER UP detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        // 键盘事件仍然绑定到document
        document.addEventListener('keydown', handleKeyboard, { passive: false });

        console.log('[FRONTEND] 🎯 Event listeners setup completed on capture layer');

        // 测试事件捕获层
        eventCaptureLayer.addEventListener('mouseover', () => {
            console.log('[FRONTEND] 🎯 Mouse entered event capture layer');
        }, { once: true });

        // 🔧 CRITICAL DEBUG: 添加更多事件测试
        eventCaptureLayer.addEventListener('mouseenter', () => {
            console.log('[FRONTEND] 🔧 MOUSE ENTER on event capture layer');
        });

        eventCaptureLayer.addEventListener('mouseleave', () => {
            console.log('[FRONTEND] 🔧 MOUSE LEAVE on event capture layer');
        });

        // 🔧 CRITICAL DEBUG: 测试点击事件是否到达事件捕获层
        eventCaptureLayer.addEventListener('click', (e) => {
            console.log('[FRONTEND] 🔧 DIRECT CLICK on event capture layer at:', e.clientX, e.clientY);
            console.log('[FRONTEND] 🔧 Event target:', e.target);
            console.log('[FRONTEND] 🔧 Current target:', e.currentTarget);
        }, { capture: true });

        // 🔧 CRITICAL DEBUG: 测试所有鼠标事件
        ['mousedown', 'mouseup', 'mousemove'].forEach(eventType => {
            eventCaptureLayer.addEventListener(eventType, (e) => {
                if (eventType !== 'mousemove' || Date.now() - lastLogTime > 1000) {
                    console.log(`[FRONTEND] 🔧 ${eventType.toUpperCase()} on event capture layer at:`, e.clientX, e.clientY);
                    if (eventType !== 'mousemove') lastLogTime = Date.now();
                }
            }, { capture: true });
        });

        // 监听模式切换事件
        if (window.__TAURI__) {
            window.__TAURI__.event.listen('mode-switch', (event) => {
                console.log('[FRONTEND] 🎯 Mode switch event received:', event.payload);
                handleModeSwitch(event.payload);
            });
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => e.preventDefault());

        console.log('[FRONTEND] Event listeners setup completed');

        // 改进的初始化逻辑
        console.log('[FRONTEND] 🔍 Checking overlay initialization state...');
        console.log('[FRONTEND] 🔍 document.hidden:', document.hidden);
        console.log('[FRONTEND] 🔍 document.hasFocus():', document.hasFocus());
        console.log('[FRONTEND] 🔍 document.visibilityState:', document.visibilityState);

        // 🔧 CRITICAL DEBUG: 立即显示状态检查结果
        updateDebugInfo('STATE CHECK: hidden=' + document.hidden + ' vis=' + document.visibilityState, 'cyan');

        // 检查是否为预初始化状态
        if (document.hidden || document.visibilityState === 'hidden') {
            console.log('[FRONTEND] 🔧 Overlay in pre-initialized/hidden state, setting up activation listeners');
            updateDebugInfo('HIDDEN STATE - Setting up listeners', 'yellow');

            // 监听窗口显示事件
            document.addEventListener('visibilitychange', function() {
                console.log('[FRONTEND] 🎯 Visibility changed - hidden:', document.hidden, 'state:', document.visibilityState);
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Overlay activated via visibility change, starting initialization...');
                    initializeOverlay();
                }
            });

            // 监听窗口焦点事件
            window.addEventListener('focus', function() {
                console.log('[FRONTEND] 🎯 Overlay focused, ensuring initialization...');
                initializeOverlay();
            });

            // 监听窗口显示事件（备用）
            window.addEventListener('load', function() {
                console.log('[FRONTEND] 🎯 Window load event, checking if should initialize...');
                if (!document.hidden) {
                    initializeOverlay();
                }
            });

            // 定期检查窗口状态（防止事件丢失）
            const checkInterval = setInterval(() => {
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Periodic check detected visible state, initializing...');
                    clearInterval(checkInterval);
                    initializeOverlay();
                }
            }, 500);

            // 5秒后清除检查间隔（防止无限检查）
            setTimeout(() => {
                clearInterval(checkInterval);
                console.log('[FRONTEND] 🔧 Periodic visibility check timeout');
            }, 5000);

        } else {
            // 立即初始化
            console.log('[FRONTEND] 🎯 Overlay visible, starting immediate initialization...');
            updateDebugInfo('VISIBLE STATE - Starting immediate init', 'lime');
            initializeOverlay();
        }

        function initializeOverlay() {
            // 🔧 CRITICAL DEBUG: 立即显示初始化开始
            try {
                updateDebugInfo('INIT STARTED!', 'blue');
            } catch (e) {
                console.error('[FRONTEND] ❌ updateDebugInfo failed:', e);
            }

            // 防止重复初始化
            if (window.overlayInitialized) {
                console.log('[FRONTEND] 🔧 Overlay already initialized, skipping...');
                updateDebugInfo('ALREADY INITIALIZED!', 'orange');
                return;
            }

            console.log('[FRONTEND] 🚀 Starting overlay initialization...');
            console.log('[FRONTEND] 🚀 Current state - hidden:', document.hidden, 'visibility:', document.visibilityState);
            console.log('[FRONTEND] 🚀 Tauri API available:', !!window.__TAURI__);

            window.overlayInitialized = true;

            // 🔧 CRITICAL DEBUG: 更新初始化状态
            updateDebugInfo('OVERLAY INITIALIZED! Ready for clicks.', 'green');

            // 🔧 IMMEDIATE TEST: Test click events right after initialization
            console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Setting up test click handler');
            setTimeout(() => {
                console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Adding test click to body');
                document.body.addEventListener('click', function(e) {
                    console.log('[FRONTEND] 🧪 BODY CLICK DETECTED!', e.clientX, e.clientY);
                }, { passive: false });

                // Test programmatic click
                console.log('[FRONTEND] 🧪 IMMEDIATE CLICK TEST - Simulating programmatic click');
                const testEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    clientX: 100,
                    clientY: 100
                });
                document.body.dispatchEvent(testEvent);
            }, 100);

            try {
                // 确保DOM元素可用
                console.log('[FRONTEND] 🚀 Checking DOM elements...');
                const requiredElements = ['windowHighlight', 'windowInfo', 'crosshair', 'mousePos', 'eventCaptureLayer'];
                for (const elementId of requiredElements) {
                    const element = document.getElementById(elementId);
                    if (!element) {
                        console.error('[FRONTEND] ❌ Required element not found:', elementId);
                        return;
                    }
                }
                console.log('[FRONTEND] ✅ All required DOM elements found');

                // 初始化窗口列表
                console.log('[FRONTEND] 🚀 Loading windows...');
                loadWindows();

                // 检查智能检测API可用性
                console.log('[FRONTEND] 🚀 Checking smart detection API...');
                checkSmartDetectionAPI();

                // 检查macOS权限
                console.log('[FRONTEND] 🚀 Checking macOS permissions...');
                checkMacOSPermissions();

                // 启用检测
                console.log('[FRONTEND] 🚀 Enabling detection...');
                detectionEnabled = true;

                console.log('[FRONTEND] ✅ Overlay initialization completed successfully');

            } catch (error) {
                console.error('[FRONTEND] ❌ Overlay initialization failed:', error);
                window.overlayInitialized = false; // 重置标志，允许重试
            }
        }

        async function loadWindows() {
            try {
                if (window.__TAURI__) {
                    const startTime = performance.now();
                    windows = await window.__TAURI__.core.invoke('modules::window::list_windows_new');
                    const endTime = performance.now();

                    windowCount.textContent = windows.length;

                    console.log('[FRONTEND] Loaded windows:', windows.length);
                }
            } catch (error) {
                console.error('[FRONTEND] Failed to load windows:', error);
                windows = [];
                windowCount.textContent = '0';
            }
        }

        async function checkSmartDetectionAPI() {
            console.log('[FRONTEND] 🔍 Checking smart detection API availability...');
            try {
                if (!window.__TAURI__) {
                    console.error('[FRONTEND] ❌ Tauri API not available');
                    return;
                }

                // 测试智能检测API
                console.log('[FRONTEND] 🔍 Testing detect_window_smart API...');
                const testResult = await window.__TAURI__.core.invoke('detect_window_smart', {
                    x: 100,
                    y: 100
                });
                console.log('[FRONTEND] ✅ Smart detection API test successful:', testResult);
                apiUsed.textContent = 'Smart ✓';
            } catch (error) {
                console.error('[FRONTEND] ❌ Smart detection API test failed:', error);
                console.error('[FRONTEND] ❌ Error details:', error.message || error);
                apiUsed.textContent = 'Smart ✗';

                // 测试降级API
                try {
                    console.log('[FRONTEND] 🔍 Testing fallback API...');
                    const fallbackResult = await window.__TAURI__.core.invoke('modules::window::detect_window_under_mouse_realtime', {
                        x: 100,
                        y: 100,
                        exclude_overlay_windows: true
                    });
                    console.log('[FRONTEND] ✅ Fallback API test successful:', fallbackResult);
                    apiUsed.textContent = 'Fallback ✓';
                } catch (fallbackError) {
                    console.error('[FRONTEND] ❌ Fallback API also failed:', fallbackError);
                    apiUsed.textContent = 'All ✗';
                }
            }
        }

        // 节流控制
        let lastDetectionTime = 0;
        let lastLogTime = 0;
        let pendingDetection = null;
        const DETECTION_THROTTLE = 100; // 增加到100ms节流，提高性能
        const LOG_THROTTLE = 1000; // 增加到1000ms日志节流，减少日志噪音

        async function handleMouseMove(event) {
            const now = Date.now();
            const x = event.clientX;
            const y = event.clientY;

            // 添加拖拽状态的鼠标移动日志
            if (isDragging && now - lastLogTime > LOG_THROTTLE) {
                console.log('[FRONTEND] 🖱️ Mouse move during drag at:', x, y);
                console.log('[FRONTEND] 🖱️ Drag in progress from:', dragStartX, dragStartY);
                lastLogTime = now;
            }

            // 改进的节流机制 - 使用防抖而不是简单节流
            if (pendingDetection) {
                clearTimeout(pendingDetection);
            }

            if (now - lastDetectionTime < DETECTION_THROTTLE) {
                // 设置延迟检测，确保最后一次鼠标移动会被处理
                pendingDetection = setTimeout(async () => {
                    try {
                        await performWindowDetection(x, y);
                    } catch (error) {
                        console.error('[FRONTEND] ❌ Delayed window detection failed:', error);
                    }
                }, DETECTION_THROTTLE);
                return;
            }

            lastDetectionTime = now;
            pendingDetection = null;

            if (now - lastLogTime > LOG_THROTTLE) {
                console.log('[FRONTEND] 🖱️ Mouse move detected at:', x, y);
                console.log('[FRONTEND] 🖱️ Detection enabled:', detectionEnabled, 'isDragging:', isDragging);
                lastLogTime = now;
            }

            if (!detectionEnabled && !isDragging) {
                console.log('[FRONTEND] ⚠️ Detection disabled, ignoring mouse move');
                return;
            }

            mousePos.textContent = `(${x}, ${y})`;

            // 更新十字线位置
            crosshair.style.left = x + 'px';
            crosshair.style.top = y + 'px';

            // 在拖拽过程中跳过窗口检测
            if (isDragging) {
                if (now - lastLogTime > LOG_THROTTLE) {
                    console.log('[FRONTEND] 🖱️ Skipping window detection during drag');
                }
                return;
            }

            // 立即执行窗口检测
            try {
                await performWindowDetection(x, y);
            } catch (error) {
                console.error('[FRONTEND] ❌ Window detection failed:', error);
                hideHighlight();
                currentWindowElement.textContent = 'Error: ' + (error.message || error);
                apiUsed.textContent = 'Error';
                processName.textContent = 'Error';
            }
        }

        // 本地检测函数已移除，现在使用后端API进行实时检测

        function handleModeSwitch(payload) {
            console.log('[FRONTEND] 🎯 Handling mode switch:', payload);

            if (payload.to === 'region_selection') {
                console.log('[FRONTEND] 🎯 Switching to region selection mode');

                // 隐藏窗口检测相关的UI
                hideHighlight();
                document.querySelector('.instructions h3').textContent = '📐 Region Selection';
                document.querySelector('.instructions p').innerHTML = 'Click and drag to select area<br><strong>ESC</strong> to exit';
                document.querySelector('.mode-indicator').textContent = 'REGION SELECT';
                document.querySelector('.mode-indicator').style.backgroundColor = 'rgba(53, 107, 255, 0.9)';

                // 禁用窗口检测
                detectionEnabled = false;

                // 启用区域选择功能
                enableRegionSelection();

                console.log('[FRONTEND] 🎯 Mode switch to region selection completed');
            }
        }

        function enableRegionSelection() {
            console.log('[FRONTEND] 🎯 Enabling region selection functionality');

            // 这里可以添加区域选择的具体实现
            // 目前先显示提示信息
            currentWindowElement.textContent = 'Region Selection Mode';
        }

        function getAppIcon(appName) {
            const iconMap = {
                'chrome': '🌐',
                'safari': '🧭',
                'firefox': '🦊',
                'edge': '🌊',
                'finder': '📁',
                'terminal': '⚡',
                'code': '💻',
                'vscode': '💻',
                'xcode': '🔨',
                'photoshop': '🎨',
                'illustrator': '✏️',
                'sketch': '📐',
                'figma': '🎯',
                'slack': '💬',
                'discord': '🎮',
                'zoom': '📹',
                'teams': '👥',
                'mail': '📧',
                'calendar': '📅',
                'notes': '📝',
                'music': '🎵',
                'spotify': '🎶',
                'vlc': '🎬',
                'quicktime': '🎥',
                'preview': '👁️',
                'calculator': '🧮',
                'system preferences': '⚙️',
                'activity monitor': '📊',
                'console': '🖥️',
                'simulator': '📱',
                'docker': '🐳',
                'postman': '📮',
                'git': '🌿',
                'github': '🐙',
                'notion': '📋',
                'obsidian': '🔮',
                'bear': '🐻',
                'typora': '📄'
            };

            const lowerAppName = appName.toLowerCase();

            // 精确匹配
            if (iconMap[lowerAppName]) {
                return iconMap[lowerAppName];
            }

            // 模糊匹配
            for (const [key, icon] of Object.entries(iconMap)) {
                if (lowerAppName.includes(key) || key.includes(lowerAppName)) {
                    return icon;
                }
            }

            // 默认图标
            return '🪟';
        }

        function highlightWindow(window) {
            currentWindowId = window.id;
            console.log('[FRONTEND] Highlighting window:', window.title, 'ID:', window.id);

            // 设置高亮区域
            windowHighlight.style.left = window.x + 'px';
            windowHighlight.style.top = window.y + 'px';
            windowHighlight.style.width = window.width + 'px';
            windowHighlight.style.height = window.height + 'px';
            windowHighlight.style.display = 'block';

            // 更新窗口信息
            const titleElement = windowInfo.querySelector('.title');
            const appNameElement = windowInfo.querySelector('.app-name');
            const dimensionsElement = windowInfo.querySelector('.dimensions');
            const appIconElement = windowInfo.querySelector('.app-icon');

            titleElement.textContent = window.title || 'Untitled Window';
            appNameElement.textContent = window.app_name || 'Unknown App';
            dimensionsElement.textContent = `${window.width} × ${window.height} at (${window.x}, ${window.y})`;

            // 设置应用图标（基于应用名称）
            appIconElement.textContent = getAppIcon(window.app_name || '');

            // 智能定位信息面板 - 避免超出屏幕边界
            const screenWidth = window.innerWidth || screen.width;
            const screenHeight = window.innerHeight || screen.height;
            const infoWidth = 350; // 信息卡片最大宽度
            const infoHeight = 120; // 信息卡片估计高度
            const margin = 15;

            let infoX, infoY;

            // 优先在窗口右上角显示
            if (window.x + window.width + margin + infoWidth <= screenWidth) {
                // 右侧有足够空间
                infoX = window.x + window.width + margin;
            } else if (window.x - margin - infoWidth >= 0) {
                // 左侧有足够空间
                infoX = window.x - margin - infoWidth;
            } else {
                // 两侧都没有足够空间，放在屏幕右边缘
                infoX = screenWidth - infoWidth - margin;
            }

            // 垂直位置：优先在窗口顶部对齐
            if (window.y + infoHeight <= screenHeight) {
                infoY = window.y;
            } else if (window.y + window.height - infoHeight >= 0) {
                // 在窗口底部对齐
                infoY = window.y + window.height - infoHeight;
            } else {
                // 放在屏幕顶部
                infoY = margin;
            }

            // 确保不超出边界
            infoX = Math.max(margin, Math.min(infoX, screenWidth - infoWidth - margin));
            infoY = Math.max(margin, Math.min(infoY, screenHeight - infoHeight - margin));

            windowInfo.style.left = infoX + 'px';
            windowInfo.style.top = infoY + 'px';
            windowInfo.style.display = 'block';
        }

        function hideHighlight() {
            currentWindowId = null;
            windowHighlight.style.display = 'none';
            windowInfo.style.display = 'none';
        }

        function handleClick(event) {
            console.log('[FRONTEND] 🖱️ Click event detected at:', event.clientX, event.clientY);
            console.log('[FRONTEND] 🖱️ Current state:', appStateManager.currentState);
            console.log('[FRONTEND] 🖱️ Selected window:', appStateManager.selectedWindow);

            // 🔧 CRITICAL FIX: 直接处理窗口选择，不依赖复杂的事件链
            if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                console.log('[FRONTEND] 🖱️ Handling window selection from click event');
                event.preventDefault(); // 防止其他处理
                event.stopPropagation(); // 防止事件冒泡
                handleWindowSelection();
                return;
            } else {
                console.log('[FRONTEND] 🖱️ Click ignored: Not in DETECTING state or no window selected.');
                console.log('[FRONTEND] 🖱️   - State:', appStateManager.currentState);
                console.log('[FRONTEND] 🖱️   - Window:', appStateManager.selectedWindow);
            }
        }

        // 处理窗口选择
        async function handleWindowSelection() {
            console.log('[FRONTEND] 🎯 Handling window selection');

            // 关键修复：在函数开始时立即捕获选定的窗口信息
            const windowToCapture = appStateManager.selectedWindow;

            if (!windowToCapture) {
                console.log('[FRONTEND] 🎯 No window to select');
                return;
            }
            console.log('[FRONTEND] 🎯 Selected window details:', windowToCapture);

            try {
                console.log('[FRONTEND] 🎯 Setting state to CAPTURING');
                appStateManager.setState(AppState.CAPTURING);

                console.log('[FRONTEND] 🎯 Calling captureSelectedWindow');
                const screenshotResult = await captureSelectedWindow(windowToCapture);

                console.log('[FRONTEND] 🎯 Screenshot result:', screenshotResult);

                if (screenshotResult && screenshotResult.success) {
                    console.log('[FRONTEND] 🎯 Screenshot captured successfully');

                    const region = {
                        x: windowToCapture.x,
                        y: windowToCapture.y,
                        width: windowToCapture.width,
                        height: windowToCapture.height
                    };

                    appStateManager.setState(AppState.EDITING, {
                        screenshotData: screenshotResult,
                        editingRegion: region
                    });

                    console.log('[FRONTEND] 🎯 Calling displayScreenshotInEditingMode');
                    displayScreenshotInEditingMode(screenshotResult, region);
                } else {
                    console.error('[FRONTEND] 🎯 Screenshot capture failed:', screenshotResult);
                    appStateManager.setState(AppState.ERROR);
                }
            } catch (error) {
                console.error('[FRONTEND] 🎯 Error during window selection:', error);
                console.error('[FRONTEND] 🎯 Error type:', typeof error);
                console.error('[FRONTEND] 🎯 Error JSON:', JSON.stringify(error));
                appStateManager.setState(AppState.ERROR);
            }
        }

        // 捕获选中窗口的截图
        async function captureSelectedWindow(windowInfo) {
            console.log('[FRONTEND] 🎯 Capturing window:', windowInfo);

            try {
                // 检查Tauri API是否可用
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available');
                }

                // 使用窗口信息进行区域截图，并确保所有值为浮点数
                const area = {
                    x: parseFloat(windowInfo.x),
                    y: parseFloat(windowInfo.y),
                    width: parseFloat(windowInfo.width),
                    height: parseFloat(windowInfo.height)
                };

                console.log('[FRONTEND] 🎯 Calling capture_region_new with area:', area);

                const result = await window.__TAURI__.core.invoke('capture_region_new', {
                    area: area
                });

                console.log('[FRONTEND] 🎯 Screenshot result:', result);
                return result;
            } catch (error) {
                console.error('[FRONTEND] 🎯 Failed to capture window:', error);
                console.error('[FRONTEND] 🎯 Error details:', error.message, error.stack);
                throw error;
            }
        }

        // 在编辑模式中显示截图
        function displayScreenshotInEditingMode(screenshotResult, region) {
            console.log('[FRONTEND] 🎯 Displaying screenshot in editing mode');

            const screenshotContainer = document.getElementById('screenshotContainer');
            const screenshotImage = document.getElementById('screenshotImage');

            if (screenshotResult.base64) {
                // 设置截图图片
                screenshotImage.src = `data:image/png;base64,${screenshotResult.base64}`;

                // 设置容器位置和大小
                screenshotContainer.style.left = `${region.x}px`;
                screenshotContainer.style.top = `${region.y}px`;
                screenshotContainer.style.width = `${region.width}px`;
                screenshotContainer.style.height = `${region.height}px`;

                console.log('[FRONTEND] 🎯 Screenshot displayed at:', region);

                // 将窗口坐标传递给编辑组件（通过全局事件）
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('window-selected', {
                        x: region.x,
                        y: region.y,
                        width: region.width,
                        height: region.height,
                        imageData: screenshotResult.base64
                    });
                    console.log('[FRONTEND] 🎯 Window coordinates sent to editor:', region);
                }
            } else {
                console.error('[FRONTEND] 🎯 No base64 data in screenshot result');
            }
        }

        function showSelectedWindow() {
            // 创建暗化遮罩
            if (!dimOverlay) {
                dimOverlay = document.createElement('div');
                dimOverlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    z-index: 999;
                    pointer-events: none;
                `;
                document.body.appendChild(dimOverlay);
            }
            dimOverlay.style.display = 'block';

            // 修改窗口高亮样式为选中状态
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '3px solid #FF6B35';
            windowHighlight.style.zIndex = '1000';
        }

        function showQuickActionToolbar() {
            if (!quickToolbar) {
                createQuickActionToolbar();
            }

            // 定位工具栏到选中窗口的右下角
            const windowRect = windowHighlight.getBoundingClientRect();
            const toolbarX = windowRect.right - 200; // 工具栏宽度约200px
            const toolbarY = windowRect.bottom + 10;

            quickToolbar.style.left = toolbarX + 'px';
            quickToolbar.style.top = toolbarY + 'px';
            quickToolbar.style.display = 'flex';

            console.log('[FRONTEND] 🛠️ Quick action toolbar displayed');
        }

        function createQuickActionToolbar() {
            quickToolbar = document.createElement('div');
            quickToolbar.style.cssText = `
                position: fixed;
                display: none;
                flex-direction: row;
                gap: 8px;
                padding: 12px;
                background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
                border: 1px solid rgba(255, 107, 53, 0.3);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(10px);
                z-index: 1002;
                pointer-events: auto;
            `;

            // 创建工具栏按钮
            const actions = [
                { icon: '📐', title: '矩形', action: 'rectangle' },
                { icon: '⭕', title: '椭圆', action: 'ellipse' },
                { icon: '📝', title: '文字', action: 'text' },
                { icon: '🖌️', title: '画笔', action: 'brush' },
                { icon: '➡️', title: '箭头', action: 'arrow' },
                { icon: '🔢', title: '序号', action: 'number' },
                { icon: '📏', title: '直线', action: 'line' },
                { icon: '⚡', title: '虚线', action: 'dashed' },
                { icon: '🔲', title: '马赛克', action: 'mosaic' },
                { icon: '📌', title: '固定', action: 'pin' },
                { icon: '↶', title: '撤销', action: 'undo' },
                { icon: '🎬', title: '录制', action: 'record' },
                { icon: '❌', title: '关闭', action: 'close' },
                { icon: '✅', title: '确认', action: 'confirm' }
            ];

            actions.forEach(actionDef => {
                const button = document.createElement('button');
                button.style.cssText = `
                    width: 36px;
                    height: 36px;
                    border: none;
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                `;

                button.textContent = actionDef.icon;
                button.title = actionDef.title;
                button.onclick = () => handleToolbarAction(actionDef.action);

                // 悬停效果
                button.onmouseenter = () => {
                    button.style.background = 'rgba(255, 107, 53, 0.3)';
                    button.style.transform = 'scale(1.1)';
                };
                button.onmouseleave = () => {
                    button.style.background = 'rgba(255, 255, 255, 0.1)';
                    button.style.transform = 'scale(1)';
                };

                quickToolbar.appendChild(button);
            });

            document.body.appendChild(quickToolbar);
        }

        function handleToolbarAction(action) {
            console.log('[FRONTEND] 🛠️ Toolbar action:', action);

            switch (action) {
                case 'close':
                    exitWindowSelection();
                    break;
                case 'confirm':
                    captureSelectedWindow();
                    break;
                case 'pin':
                    pinSelectedWindow();
                    break;
                default:
                    // 其他编辑工具暂时显示提示
                    console.log('[FRONTEND] 🛠️ Edit tool not implemented yet:', action);
                    break;
            }
        }

        function exitWindowSelection() {
            console.log('[FRONTEND] 🚪 Exiting window selection mode');

            // 隐藏工具栏
            if (quickToolbar) {
                quickToolbar.style.display = 'none';
            }

            // 隐藏暗化遮罩
            if (dimOverlay) {
                dimOverlay.style.display = 'none';
            }

            // 重置窗口高亮样式
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '2px solid #FF6B35';

            // 重置状态
            isWindowSelected = false;
            selectedWindowId = null;

            // 重新启用窗口检测
            detectionEnabled = true;
        }

        async function captureSelectedWindow() {
            console.log('[FRONTEND] 📸 Capturing selected window:', selectedWindowId);

            try {
                if (window.__TAURI__) {
                    await window.__TAURI__.core.invoke('capture_selected_window', {
                        window_id: selectedWindowId
                    });
                    console.log('[FRONTEND] 📸 Window capture completed');
                    // 退出截图模式
                    await handleEscapeKey();
                }
            } catch (error) {
                console.error('[FRONTEND] 📸 Failed to capture window:', error);
            }
        }

        async function pinSelectedWindow() {
            console.log('[FRONTEND] 📌 Pinning selected window:', selectedWindowId);
            // TODO: 实现固定功能
        }

        async function handleKeyboard(event) {
            console.log('[FRONTEND] 🎹 Key pressed:', event.key, 'Current state:', appStateManager.currentState);

            switch (event.key.toLowerCase()) {
                case 'escape':
                    console.log('[FRONTEND] 🎹 ESC key detected');
                    await handleEscapeKey();
                    break;
                case 'r':
                    // 刷新窗口列表（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        loadWindows();
                    }
                    break;
                case ' ':
                    // 空格键切换到区域选择模式（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        await handleSpacebarPress();
                    }
                    break;
                case 'd':
                    // 切换检测模式（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        detectionEnabled = !detectionEnabled;
                        if (!detectionEnabled) {
                            hideHighlight();
                        }
                    }
                    break;
            }
        }

        async function handleEscapeKey() {
            console.log('[FRONTEND] 🚪 ESC key pressed, current state:', appStateManager.currentState);

            // 根据当前状态处理ESC键
            switch (appStateManager.currentState) {
                case AppState.EDITING:
                    // 从编辑模式返回检测模式
                    console.log('[FRONTEND] 🚪 Exiting editing mode');
                    appStateManager.setState(AppState.DETECTING, {
                        selectedWindow: null,
                        screenshotData: null,
                        editingRegion: null
                    });
                    break;
                case AppState.CAPTURING:
                    // 取消截图操作
                    console.log('[FRONTEND] 🚪 Canceling capture operation');
                    appStateManager.setState(AppState.DETECTING);
                    break;
                case AppState.DETECTING:
                default:
                    // 完全退出应用
                    console.log('[FRONTEND] 🚪 Exiting application');
                    await exitApplication();
                    break;
            }
        }

        async function exitApplication() {
            console.log('[FRONTEND] 🚪 Attempting to exit application');
            console.log('[FRONTEND] 🚪 Tauri available:', !!window.__TAURI__);

            try {
                if (window.__TAURI__) {
                    console.log('[FRONTEND] 🚪 Calling exit_capture_completely_direct...');
                    await window.__TAURI__.core.invoke('exit_capture_completely_direct');
                    console.log('[FRONTEND] 🚪 Direct exit command completed successfully');
                } else {
                    console.error('[FRONTEND] 🚪 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🚪 Failed to exit capture directly:', error);
                console.error('[FRONTEND] 🚪 Error details:', error.message || error);
                // 降级处理：直接关闭当前窗口
                try {
                    console.log('[FRONTEND] 🚪 Attempting fallback window close...');
                    await window.__TAURI__.window.getCurrent().close();
                    console.log('[FRONTEND] 🚪 Fallback window close completed');
                } catch (closeError) {
                    console.error('[FRONTEND] 🚪 Failed to close window:', closeError);
                }
            }
        }

        async function handleSpacebarPress() {
            try {
                if (window.__TAURI__) {
                    const overlayId = window.location.hash.substring(1) || 'window_highlight_overlay';
                    await window.__TAURI__.core.invoke('handle_spacebar_press', {
                        window_id: overlayId
                    });
                    console.log('Spacebar handled - switching to region selection mode');
                }
            } catch (error) {
                console.error('Failed to handle spacebar press:', error);
            }
        }

        function handleMouseDown(event) {
            console.log('[FRONTEND] 🖱️ Mouse down detected at:', event.clientX, event.clientY);
            console.log('[FRONTEND] 🖱️ Event target:', event.target.className || event.target.tagName);
            console.log('[FRONTEND] 🖱️ Event capture layer:', event.target === eventCaptureLayer);
            console.log('[FRONTEND] 🖱️ Current state:', appStateManager.currentState);
            console.log('[FRONTEND] 🖱️ Selected window:', appStateManager.selectedWindow);

            isDragging = true;
            dragStartX = event.clientX;
            dragStartY = event.clientY;

            // 为可能的拖拽操作做准备，但不立即隐藏高亮
            // hideHighlight();

            console.log('[FRONTEND] 🖱️ Drag state initialized - start position:', dragStartX, dragStartY);
        }

        function handleMouseUp(event) {
            console.log('[FRONTEND] 🖱️ Mouse up detected at:', event.clientX, event.clientY);
            console.log('[FRONTEND] 🖱️ isDragging state:', isDragging);
            console.log('[FRONTEND] 🖱️ Current state:', appStateManager.currentState);
            console.log('[FRONTEND] 🖱️ Selected window:', appStateManager.selectedWindow);

            if (isDragging) {
                const dragEndX = event.clientX;
                const dragEndY = event.clientY;
                const dragDistance = Math.sqrt(
                    Math.pow(dragEndX - dragStartX, 2) + Math.pow(dragEndY - dragStartY, 2)
                );

                console.log('[FRONTEND] 🖱️ Drag calculation:');
                console.log('[FRONTEND] 🖱️   Start position:', dragStartX, dragStartY);
                console.log('[FRONTEND] 🖱️   End position:', dragEndX, dragEndY);
                console.log('[FRONTEND] 🖱️   Drag distance:', dragDistance);
                console.log('[FRONTEND] 🖱️   Threshold (10px):', dragDistance > 10);

                if (dragDistance > 10) {
                    console.log('[FRONTEND] 🎯 Drag threshold exceeded! Switching to region selection mode');
                    handleMouseDragStart(dragStartX, dragStartY);
                } else {
                    console.log('[FRONTEND] 🖱️ Drag distance is small, treating as a click.');
                    // 检查是否在检测模式下，并且有选中的窗口
                    if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                        console.log('[FRONTEND] 🖱️ Triggering window selection from mouse up.');
                        handleWindowSelection();
                    } else {
                        console.log('[FRONTEND] 🖱️ Click ignored: Not in DETECTING state or no window selected.');
                    }
                }
            } else {
                console.log('[FRONTEND] 🖱️ Mouse up without drag state');
                // 直接点击处理（非拖拽结束）
                if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                    console.log('[FRONTEND] 🖱️ Handling direct click on window');
                    handleWindowSelection();
                }
            }

            isDragging = false;
            console.log('[FRONTEND] 🖱️ Drag state reset');
        }

        // 独立的窗口检测函数，支持异步调用和错误处理
        async function performWindowDetection(x, y) {
            // 增加状态检查，确保只在检测模式下运行
            if (appStateManager.currentState !== AppState.DETECTING) {
                return;
            }
            // 增加状态检查，确保只在检测模式下运行
            if (appStateManager.currentState !== AppState.DETECTING) {
                return;
            }
            try {
                const startTime = performance.now();

                if (Date.now() - lastLogTime > LOG_THROTTLE) {
                    console.log('[FRONTEND] 🧠 Performing window detection at:', x, y);
                    lastLogTime = Date.now();
                }

                // 使用新的智能窗口检测API
                let detectedWindow = null;
                try {
                    detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_cursor', {
                        x: x,
                        y: y
                    });

                    if (Date.now() - lastLogTime > LOG_THROTTLE) {
                        console.log('[FRONTEND] 🧠 Smart detection result:', detectedWindow);
                    }
                } catch (smartError) {
                    console.warn('[FRONTEND] 🧠 Smart detection failed:', smartError.message || smartError);

                    // 降级到实时检测API
                    try {
                        detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_mouse_realtime', {
                            x: x,
                            y: y,
                            exclude_overlay_windows: true
                        });
                        console.log('[FRONTEND] 🧠 Fallback detection result:', detectedWindow);
                    } catch (fallbackError) {
                        console.error('[FRONTEND] 🧠 Both detection methods failed:', fallbackError);
                        return;
                    }
                }

                const endTime = performance.now();
                const detectionTimeMs = Math.round(endTime - startTime);
                detectionTime.textContent = `${detectionTimeMs}ms`;

                if (detectedWindow) {
                    const windowTitle = detectedWindow.title || detectedWindow.app_name || 'No title';
                    const windowProcessName = detectedWindow.process_name || detectedWindow.app_name || 'Unknown';

                    if (Date.now() - lastLogTime > LOG_THROTTLE) {
                        console.log('[FRONTEND] 🧠 Window detected:', windowTitle, 'Process:', windowProcessName);
                    }

                    // 缓存检测到的窗口信息到状态管理器
                    appStateManager.selectedWindow = detectedWindow;

                    highlightWindow(detectedWindow);
                    currentWindowElement.textContent = windowTitle.substring(0, 20) + (windowTitle.length > 20 ? '...' : '');
                    apiUsed.textContent = 'Smart';
                    processName.textContent = windowProcessName.substring(0, 15);

                    // 更新窗口计数
                    windowCount.textContent = '1';
                } else {
                    if (Date.now() - lastLogTime > LOG_THROTTLE) {
                        console.log('[FRONTEND] 🧠 No window detected at position');
                    }

                    // 清除缓存的窗口信息
                    appStateManager.selectedWindow = null;

                    hideHighlight();
                    currentWindowElement.textContent = 'None';
                    apiUsed.textContent = 'Smart';
                    processName.textContent = '-';
                    windowCount.textContent = '0';
                }
            } catch (error) {
                console.error('[FRONTEND] ❌ Window detection error:', error.message || error);

                // 显示错误状态
                hideHighlight();
                currentWindowElement.textContent = 'Error';
                apiUsed.textContent = 'Error';
                processName.textContent = 'Error';
                detectionTime.textContent = 'Error';
            }
        }

        async function handleMouseDragStart(startX, startY) {
            console.log('[FRONTEND] 🎯 handleMouseDragStart called with:', startX, startY);

            try {
                if (window.__TAURI__) {
                    const overlayId = window.location.hash.substring(1) || 'window_highlight_overlay';
                    console.log('[FRONTEND] 🎯 Calling backend with overlay ID:', overlayId);
                    console.log('[FRONTEND] 🎯 Invoking handle_mouse_drag_start...');

                    const result = await window.__TAURI__.core.invoke('handle_mouse_drag_start', {
                        window_id: overlayId,
                        start_x: startX,
                        start_y: startY
                    });

                    console.log('[FRONTEND] 🎯 Backend response:', result);
                    console.log('[FRONTEND] 🎯 Mouse drag start handled - switching to region selection mode');
                } else {
                    console.error('[FRONTEND] 🎯 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🎯 Failed to handle mouse drag start:', error);
                console.error('[FRONTEND] 🎯 Error details:', error.message || error);
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 检查macOS权限
        async function checkMacOSPermissions() {
            try {
                if (!window.__TAURI__) {
                    console.log('[FRONTEND] 🔍 Tauri API not available, skipping permission check');
                    return;
                }

                console.log('[FRONTEND] 🔍 Checking macOS permissions...');

                // 使用安全的权限检查方式，避免调用受限API
                try {
                    const hasPermission = await window.__TAURI__.core.invoke('check_macos_permissions');

                    if (hasPermission) {
                        console.log('[FRONTEND] ✅ macOS screen recording permission granted');
                        updateStatus('API', 'Smart');
                    } else {
                        console.warn('[FRONTEND] ⚠️ macOS screen recording permission not granted');
                        updateStatus('API', 'Limited');
                        showPermissionWarning();
                    }
                } catch (permissionError) {
                    console.warn('[FRONTEND] ⚠️ Permission check API not available:', permissionError.message || permissionError);
                    // 降级处理：假设有权限，让用户在实际使用时发现问题
                    updateStatus('API', 'Unknown');
                }
            } catch (error) {
                console.error('[FRONTEND] ❌ Failed to check macOS permissions:', error);
                // 如果权限检查失败，可能是非macOS平台
                console.log('[FRONTEND] 🔍 Permission check failed, likely not macOS platform');
                updateStatus('API', 'Unknown');
            }
        }

        // 显示权限警告
        function showPermissionWarning() {
            // 在API状态中显示权限警告
            const apiElement = document.getElementById('apiUsed');
            if (apiElement) {
                apiElement.style.color = 'orange';
                apiElement.textContent = 'Permission Required';
            }
        }

        // 定期刷新窗口列表
        setInterval(() => {
            if (detectionEnabled) {
                loadWindows();
            }
        }, 2000);

        // 确保透明度设置
        document.body.style.setProperty('background-color', 'transparent', 'important');
        document.documentElement.style.setProperty('background-color', 'transparent', 'important');

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            console.error('[FRONTEND] 🚨 Global error:', event.error);
            console.error('[FRONTEND] 🚨 Error message:', event.message);
            console.error('[FRONTEND] 🚨 Error filename:', event.filename);
            console.error('[FRONTEND] 🚨 Error line:', event.lineno);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('[FRONTEND] 🚨 Unhandled promise rejection:', event.reason);
        });

        console.log('[FRONTEND] 🎯 Window highlight overlay loaded');
        console.log('[FRONTEND] 🎯 Body background:', window.getComputedStyle(document.body).backgroundColor);
        console.log('[FRONTEND] 🎯 HTML background:', window.getComputedStyle(document.documentElement).backgroundColor);

        // 立即检查macOS权限（不等待）
        setTimeout(() => {
            checkMacOSPermissions().catch(error => {
                console.error('[FRONTEND] 🚨 macOS permission check failed:', error);
            });
        }, 100);

        // 检查窗口焦点状态
        console.log('[FRONTEND] 🎯 Document has focus:', document.hasFocus());
        console.log('[FRONTEND] 🎯 Active element:', document.activeElement.tagName);
        console.log('[FRONTEND] 🎯 Tauri available:', !!window.__TAURI__);

        // 强制设置焦点到document
        if (!document.hasFocus()) {
            console.log('[FRONTEND] 🎯 Document does not have focus, attempting to focus...');
            window.focus();
            document.body.focus();
        }

        // 测试键盘事件监听
        console.log('[FRONTEND] 🎯 Testing keyboard event listener...');
        console.log('[FRONTEND] 🎯 Event listeners attached to document');

        // 立即测试一个键盘事件
        document.addEventListener('keydown', (e) => {
            console.log('[FRONTEND] 🎯 IMMEDIATE KEY TEST - Key pressed:', e.key);
        }, { once: true });

        setTimeout(() => {
            console.log('[FRONTEND] 🎯 Please press ESC key to test...');
            console.log('[FRONTEND] 🎯 Current focus element:', document.activeElement);
            console.log('[FRONTEND] 🎯 Document visibility:', document.visibilityState);
        }, 1000);
    </script>
</body>
</html>
