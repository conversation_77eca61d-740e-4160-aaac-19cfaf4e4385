[package]
name = "mecap"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "mecap_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [ "protocol-asset", "macos-private-api"] }
tauri-plugin-opener = "2"
tauri-plugin-fs = "2"
tauri-plugin-shell = "2"
tauri-plugin-notification = "2"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-global-shortcut = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
xcap = "0.5.2"
lazy_static = "1.4"
image = "0.25"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
dirs = "5.0"
base64 = "0.22"
tokio = { version = "1", features = ["time"] }
# 混合截图功能依赖
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"

# 平台特定依赖配置
[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25"
core-graphics = "0.23"
core-foundation = "0.9"
objc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
windows = { version = "0.54", features = [
    "Win32_UI_WindowsAndMessaging",
    "Win32_Foundation",
    "Win32_Graphics_Gdi",
    "Win32_System_Threading",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_System_ProcessStatus"
] }

[target.'cfg(target_os = "linux")'.dependencies]
x11rb = { version = "0.13", optional = true }
wayland-client = { version = "0.31", optional = true }

# 特性标志配置
[features]
default = ["smart-window-detection"]
smart-window-detection = []
x11 = ["dep:x11rb"]
wayland = ["dep:wayland-client"]

