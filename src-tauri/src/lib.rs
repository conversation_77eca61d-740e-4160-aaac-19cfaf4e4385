mod screenshot;
mod overlay;
mod modules;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志系统
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Debug)
        .init();

    log::info!("[MECAP] Starting Mecap application with enhanced logging");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(
            tauri_plugin_global_shortcut::Builder::new()
                .with_handler(|app_handle, shortcut, event| {
                    use tauri_plugin_global_shortcut::{Code, Shortcut, ShortcutState};

                    // 检查是否是ESC键
                    let esc_shortcut = Shortcut::new(None, Code::Escape);
                    if shortcut == &esc_shortcut {
                        match event.state() {
                            ShortcutState::Pressed => {
                                println!("[GLOBAL-SHORTCUT] 🎯 ESC key pressed globally - triggering exit");

                                // 异步调用退出函数
                                let app_handle_clone = app_handle.clone();
                                tauri::async_runtime::spawn(async move {
                                    if let Err(e) = modules::ux::exit_capture_completely_direct(app_handle_clone).await {
                                        println!("[GLOBAL-SHORTCUT] ❌ Failed to exit capture: {}", e);
                                    } else {
                                        println!("[GLOBAL-SHORTCUT] ✅ Successfully exited capture via global shortcut");
                                    }
                                });
                            }
                            ShortcutState::Released => {
                                // ESC键释放，不需要处理
                            }
                        }
                    }
                })
                .build()
        )
        .invoke_handler(tauri::generate_handler![
            // 原有截图功能（保持向后兼容）
            screenshot::capture_region,
            screenshot::capture_fullscreen,
            screenshot::capture_window,
            screenshot::get_screens,
            screenshot::capture_fullscreen_preview,
            screenshot::save_screenshot_from_preview,
            screenshot::save_edited_screenshot,
            screenshot::get_pictures_dir,
            screenshot::list_windows,
            screenshot::capture_window_preview,
            screenshot::check_screen_recording_permission,
            screenshot::request_screen_recording_permission,
            screenshot::validate_window_exists,
            screenshot::get_window_info,
            screenshot::clear_window_cache,
            screenshot::start_region_selection,
            screenshot::complete_region_selection,
            // screenshot::get_window_under_cursor,
            modules::window::detect_window_under_mouse,
            screenshot::start_window_hover_mode,
            screenshot::stop_window_hover_mode,
            screenshot::run_performance_benchmark,
            screenshot::hide_main_window,
            screenshot::show_main_window,

            // Phase 1: 新的模块化命令
            modules::capture::capture_region_new,
            modules::capture::capture_fullscreen_new,
            modules::capture::capture_fullscreen_preview_new,
            modules::capture::get_capture_config,
            modules::window::list_windows_new,
            modules::window::refresh_window_list,
            modules::window::get_window_info_new,
            modules::window::validate_window_exists_new,
            modules::window::clear_window_cache_new,
            modules::window::detect_window_under_mouse,
            modules::window::detect_window_under_mouse_realtime,
            modules::window::benchmark_window_detection,
            modules::window::start_smart_window_detection,
            modules::window::stop_smart_window_detection,
            modules::window::detect_window_under_cursor,
            modules::window::get_window_details,
            modules::window::capture_selected_window,
            modules::window::detect_window_under_mouse,
            modules::window::get_window_detection_config,
            modules::window::update_window_detection_config,
            modules::recording::start_recording,
            modules::recording::stop_recording,
            modules::recording::pause_recording,
            modules::recording::resume_recording,
            modules::recording::get_recording_status,
            modules::recording::check_recording_capabilities,
            modules::recording::get_recommended_recording_config,
            modules::overlay::create_modern_overlay,
            modules::overlay::close_overlay_new,
            modules::overlay::list_active_overlays_new,
            modules::overlay::update_overlay_properties,
            modules::overlay::get_overlay_performance_stats,
            modules::overlay::ping_test,
            modules::overlay::create_fullscreen_overlay_manager,
            modules::overlay::close_all_overlays,
            modules::overlay::hide_all_overlays,
            modules::overlay::get_overlay_compatibility_info,
            // 混合截图功能
            modules::overlay::create_multi_monitor_screenshot_overlay,
            modules::overlay::close_all_screenshot_overlays,
            // Phase 1: P1-T4 高级覆盖层UI功能
            modules::overlay::create_advanced_region_selector,
            modules::overlay::handle_region_selection_complete,
            modules::overlay::create_window_highlight_overlay,
            modules::overlay::handle_window_selection_complete,
            modules::overlay::get_overlay_ui_config,
            modules::overlay::update_overlay_ui_config,
            // Phase 1: P1-T5 快捷操作栏功能
            modules::overlay::create_quick_action_toolbar,
            modules::overlay::handle_toolbar_action,
            modules::overlay::update_toolbar_position,
            modules::overlay::get_quick_action_config,
            modules::overlay::update_quick_action_config,
            // Phase 1: 原生菜单系统
            modules::menu::create_native_menu,
            modules::menu::get_menu_config,
            modules::menu::update_menu_config,
            // Phase 1: P1-T6 状态管理系统
            modules::state::get_app_state,
            modules::state::get_capture_state,
            modules::state::update_capture_state,
            modules::state::get_editor_state,
            modules::state::update_editor_state,
            modules::state::reset_capture_state,
            modules::state::reset_editor_state,
            modules::state::reset_all_states,
            modules::state::start_capture_session,
            modules::state::complete_capture_session,
            modules::state::start_editor_session,
            modules::state::add_edit_action,
            modules::state::subscribe_to_state_changes,
            modules::state::unsubscribe_from_state_changes,
            modules::state::get_state_stats,
            // Phase 1: P1-T7 用户体验优化
            modules::ux::get_ux_config,
            modules::ux::update_ux_config,
            modules::ux::set_interaction_mode,
            modules::ux::get_interaction_mode,
            modules::ux::send_user_feedback,
            modules::ux::handle_escape_key,
            modules::ux::exit_capture_completely_direct,
            modules::ux::cycle_interaction_mode,
            modules::ux::reset_ux_state,
            modules::ux::get_ux_stats,
            modules::ux::apply_visual_theme,
            modules::ux::handle_spacebar_press,
            modules::ux::handle_mouse_drag_start,

            // 混合截图核心命令
            modules::hybrid_screenshot::start_hybrid_screenshot,
            modules::hybrid_screenshot::capture_region_from_overlay,
            modules::hybrid_screenshot::close_hybrid_screenshot,
            modules::hybrid_screenshot::get_current_monitors,
            modules::hybrid_screenshot::detect_window_smart,
            modules::hybrid_screenshot::capture_selected_window,
            #[cfg(target_os = "windows")]
            modules::hybrid_screenshot::detect_window_enhanced_windows,
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::check_macos_permissions,
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::request_macos_permissions,

            // 坐标系统命令
            modules::coordinate::get_monitor_info,
            modules::coordinate::get_virtual_screen_bounds,
            modules::coordinate::get_monitor_at_point,
            modules::coordinate::validate_screen_region,
            // 已通过modules::capture::capture_region_new引用
        ])
        .setup(|app| {
            // 设置原生菜单
            if let Err(e) = modules::menu::setup_menu_event_handler(app.handle()) {
                eprintln!("Failed to setup menu event handler: {}", e);
            }

            // 创建默认菜单
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                if let Err(e) = modules::menu::create_native_menu(app_handle.clone(), None).await {
                    eprintln!("Failed to create native menu: {}", e);
                }
                println!("[STARTUP] ✅ Application startup completed - overlay system will be created on demand");
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
