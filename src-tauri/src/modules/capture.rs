// 截图捕获模块 - 专门处理截图相关功能
use serde::{Deserialize, Serialize};
use tauri::command;
use xcap::Monitor;
use base64::{Engine as _, engine::general_purpose};
use std::time::Instant;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ScreenshotArea {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowCaptureRequest {
    pub window_id: u32,
    pub window_title: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    pub success: bool,
    pub message: String,
    pub path: Option<String>,
    pub base64: Option<String>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub capture_time_ms: Option<u64>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotPreview {
    pub base64: String,
    pub width: u32,
    pub height: u32,
    pub capture_time_ms: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CaptureConfig {
    pub enable_optimized_capture: bool,
    pub default_format: String,
    pub quality: u8,
    pub enable_preview: bool,
    pub max_capture_size: u32,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            enable_optimized_capture: true,
            default_format: "png".to_string(),
            quality: 95,
            enable_preview: true,
            max_capture_size: 4096,
        }
    }
}

/// 捕获指定区域的截图
#[command]
pub async fn capture_region_new(area: ScreenshotArea) -> Result<ScreenshotResult, String> {
    println!("[CAPTURE] Starting region capture: x={}, y={}, w={}, h={}", area.x, area.y, area.width, area.height);
    println!("[DEBUG] Parameter type: ScreenshotArea, size: {} bytes", std::mem::size_of_val(&area));
    
    let start_time = Instant::now();
    
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;
    
    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }
    
    // 使用第一个显示器进行截图
    let monitor = &monitors[0];
    let image = monitor.capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    // 裁剪指定区域
    let cropped = image::imageops::crop_imm(&image,
        area.x as u32,
        area.y as u32,
        area.width as u32,
        area.height as u32
    ).to_image();
    
    let capture_time = start_time.elapsed().as_millis() as u64;
    
    // 转换为base64
    let mut buffer = Vec::new();
    cropped.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Png)
        .map_err(|e| format!("Failed to encode image: {}", e))?;
    
    let base64_data = general_purpose::STANDARD.encode(&buffer);

    Ok(ScreenshotResult {
        success: true,
        message: "Region captured successfully".to_string(),
        path: None,
        base64: Some(base64_data),
        width: Some(cropped.width()),
        height: Some(cropped.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 捕获全屏截图
#[command(async)]
pub async fn capture_fullscreen_new(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    println!("[CAPTURE] Starting fullscreen capture");
    
    let start_time = Instant::now();
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;
    
    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }
    
    // 使用主显示器
    let monitor = &monitors[0];
    let image = monitor.capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    let capture_time = start_time.elapsed().as_millis() as u64;
    
    // 转换为base64
    let mut buffer = Vec::new();
    image.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Png)
        .map_err(|e| format!("Failed to encode image: {}", e))?;
    
    let base64_data = general_purpose::STANDARD.encode(&buffer);
    
    // 保存文件（如果指定了路径）
    let saved_path = if let Some(path) = save_path {
        image.save(&path)
            .map_err(|e| format!("Failed to save image: {}", e))?;
        Some(path)
    } else {
        None
    };
    
    Ok(ScreenshotResult {
        success: true,
        message: "Fullscreen captured successfully".to_string(),
        path: saved_path,
        base64: Some(base64_data),
        width: Some(image.width()),
        height: Some(image.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 获取全屏截图预览（用于编辑器）
#[command(async)]
pub async fn capture_fullscreen_preview_new() -> Result<ScreenshotPreview, String> {
    println!("[CAPTURE] Starting fullscreen preview capture");
    
    let start_time = Instant::now();
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;
    
    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }
    
    let monitor = &monitors[0];
    let image = monitor.capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    let capture_time = start_time.elapsed().as_millis() as u64;
    
    // 转换为base64
    let mut buffer = Vec::new();
    image.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Png)
        .map_err(|e| format!("Failed to encode image: {}", e))?;
    
    let base64_data = general_purpose::STANDARD.encode(&buffer);
    
    Ok(ScreenshotPreview {
        base64: base64_data,
        width: image.width(),
        height: image.height(),
        capture_time_ms: capture_time,
    })
}

/// 获取截图配置
#[command(async)]
pub async fn get_capture_config() -> Result<CaptureConfig, String> {
    Ok(CaptureConfig::default())
}

/// 🔧 BUG FIX: 新增窗口截图命令，使用xcap的Window::capture_image接口
#[command(async)]
pub async fn capture_window_new(request: WindowCaptureRequest) -> Result<ScreenshotResult, String> {
    println!("[CAPTURE] 🪟 Starting window capture for window_id: {}, title: {}",
             request.window_id, request.window_title);

    let start_time = Instant::now();

    // 使用xcap获取所有窗口
    let windows = xcap::Window::all().map_err(|e| {
        let error_msg = format!("Failed to get windows: {}", e);
        println!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    // 查找目标窗口
    let target_window = windows.iter().find(|w| {
        match w.id() {
            Ok(id) => id == request.window_id,
            Err(_) => false,
        }
    }).ok_or_else(|| {
        let error_msg = format!("Window with id {} not found", request.window_id);
        println!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    let window_title = target_window.title().unwrap_or_default();
    let window_width = target_window.width().unwrap_or(0);
    let window_height = target_window.height().unwrap_or(0);

    println!("[CAPTURE] 🪟 Found target window: {} ({}x{})",
             window_title, window_width, window_height);

    // 使用xcap的Window::capture_image接口捕获窗口
    let image = target_window.capture_image().map_err(|e| {
        let error_msg = format!("Failed to capture window image: {}", e);
        println!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    println!("[CAPTURE] ✅ Window image captured successfully: {}x{}",
             image.width(), image.height());

    // 转换为base64
    let mut buffer = Vec::new();
    image.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageFormat::Png)
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);
    let capture_time = start_time.elapsed().as_millis() as u64;

    let result = ScreenshotResult {
        success: true,
        message: "Window captured successfully".to_string(),
        path: None,
        base64: Some(base64_data),
        width: Some(image.width()),
        height: Some(image.height()),
        capture_time_ms: Some(capture_time),
    };

    println!("[CAPTURE] 🪟 Window capture completed in {}ms", capture_time);
    Ok(result)
}
