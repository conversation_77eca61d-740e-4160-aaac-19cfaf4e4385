// 坐标系统模块 - 处理多显示器环境下的坐标转换
use serde::{Deserialize, Serialize};
use xcap::Monitor;
use anyhow::{Result, anyhow};

/// 全局坐标点
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct GlobalCoordinate {
    pub x: i32,
    pub y: i32,
    pub monitor_index: Option<usize>,
}

/// 屏幕区域定义
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct ScreenRegion {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub monitor_index: usize,
}

/// 显示器信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorInfo {
    pub index: usize,
    pub name: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub scale_factor: f64,
    pub is_primary: bool,
}

/// 坐标系统管理器
#[derive(<PERSON>lone)]
pub struct CoordinateSystem {
    monitors: Vec<MonitorInfo>,
}

impl CoordinateSystem {
    /// 创建新的坐标系统实例
    pub fn new() -> Result<Self> {
        let monitors = Self::detect_monitors()?;
        Ok(Self { monitors })
    }

    /// 检测所有显示器
    fn detect_monitors() -> Result<Vec<MonitorInfo>> {
        let xcap_monitors = Monitor::all()
            .map_err(|e| anyhow!("Failed to get monitors: {}", e))?;

        let mut monitors = Vec::new();
        for (index, monitor) in xcap_monitors.iter().enumerate() {
            let monitor_info = MonitorInfo {
                index,
                name: monitor.name().unwrap_or_else(|_| format!("Monitor {}", index)),
                x: monitor.x().unwrap_or(0),
                y: monitor.y().unwrap_or(0),
                width: monitor.width().unwrap_or(1920),
                height: monitor.height().unwrap_or(1080),
                scale_factor: monitor.scale_factor().unwrap_or(1.0) as f64,
                is_primary: monitor.is_primary().unwrap_or(false),
            };
            monitors.push(monitor_info);
        }

        if monitors.is_empty() {
            return Err(anyhow!("No monitors detected"));
        }

        Ok(monitors)
    }

    /// 刷新显示器信息
    pub fn refresh(&mut self) -> Result<()> {
        self.monitors = Self::detect_monitors()?;
        Ok(())
    }

    /// 获取所有显示器信息
    pub fn get_monitors(&self) -> &[MonitorInfo] {
        &self.monitors
    }

    /// 获取主显示器
    pub fn get_primary_monitor(&self) -> Option<&MonitorInfo> {
        self.monitors.iter().find(|m| m.is_primary)
            .or_else(|| self.monitors.first())
    }

    /// 根据坐标点确定所属显示器
    pub fn get_monitor_at_point(&self, x: i32, y: i32) -> Option<&MonitorInfo> {
        self.monitors.iter().find(|monitor| {
            x >= monitor.x 
                && x < monitor.x + monitor.width as i32
                && y >= monitor.y 
                && y < monitor.y + monitor.height as i32
        })
    }

    /// 将全局坐标转换为相对于指定显示器的坐标
    pub fn global_to_monitor_coordinate(&self, global: &GlobalCoordinate) -> Result<(i32, i32, usize)> {
        let monitor = if let Some(index) = global.monitor_index {
            self.monitors.get(index)
                .ok_or_else(|| anyhow!("Invalid monitor index: {}", index))?
        } else {
            self.get_monitor_at_point(global.x, global.y)
                .ok_or_else(|| anyhow!("No monitor found at point ({}, {})", global.x, global.y))?
        };

        let relative_x = global.x - monitor.x;
        let relative_y = global.y - monitor.y;

        Ok((relative_x, relative_y, monitor.index))
    }

    /// 将屏幕区域转换为xcap兼容的坐标
    pub fn region_to_xcap_coordinates(&self, region: &ScreenRegion) -> Result<(i32, i32, u32, u32, usize)> {
        let monitor = self.monitors.get(region.monitor_index)
            .ok_or_else(|| anyhow!("Invalid monitor index: {}", region.monitor_index))?;

        // 转换为相对于目标显示器的坐标
        let relative_x = region.x - monitor.x;
        let relative_y = region.y - monitor.y;

        // 边界检查
        if relative_x < 0 || relative_y < 0 {
            return Err(anyhow!("Region coordinates are outside monitor bounds"));
        }

        if relative_x + region.width as i32 > monitor.width as i32 
            || relative_y + region.height as i32 > monitor.height as i32 {
            return Err(anyhow!("Region extends beyond monitor bounds"));
        }

        Ok((relative_x, relative_y, region.width, region.height, region.monitor_index))
    }

    /// 将相对坐标转换为全局坐标
    pub fn monitor_to_global_coordinate(&self, monitor_index: usize, x: i32, y: i32) -> Result<GlobalCoordinate> {
        let monitor = self.monitors.get(monitor_index)
            .ok_or_else(|| anyhow!("Invalid monitor index: {}", monitor_index))?;

        Ok(GlobalCoordinate {
            x: monitor.x + x,
            y: monitor.y + y,
            monitor_index: Some(monitor_index),
        })
    }

    /// 计算覆盖所有显示器的边界矩形
    pub fn get_virtual_screen_bounds(&self) -> Result<ScreenRegion> {
        if self.monitors.is_empty() {
            return Err(anyhow!("No monitors available"));
        }

        let mut min_x = i32::MAX;
        let mut min_y = i32::MAX;
        let mut max_x = i32::MIN;
        let mut max_y = i32::MIN;

        for monitor in &self.monitors {
            min_x = min_x.min(monitor.x);
            min_y = min_y.min(monitor.y);
            max_x = max_x.max(monitor.x + monitor.width as i32);
            max_y = max_y.max(monitor.y + monitor.height as i32);
        }

        Ok(ScreenRegion {
            x: min_x,
            y: min_y,
            width: (max_x - min_x) as u32,
            height: (max_y - min_y) as u32,
            monitor_index: 0, // 虚拟屏幕不属于特定显示器
        })
    }

    /// 验证区域是否在有效范围内
    pub fn validate_region(&self, region: &ScreenRegion) -> Result<()> {
        let monitor = self.monitors.get(region.monitor_index)
            .ok_or_else(|| anyhow!("Invalid monitor index: {}", region.monitor_index))?;

        if region.x < monitor.x 
            || region.y < monitor.y
            || region.x + region.width as i32 > monitor.x + monitor.width as i32
            || region.y + region.height as i32 > monitor.y + monitor.height as i32 {
            return Err(anyhow!("Region is outside monitor bounds"));
        }

        if region.width == 0 || region.height == 0 {
            return Err(anyhow!("Region has zero width or height"));
        }

        Ok(())
    }

    /// 获取显示器总数
    pub fn monitor_count(&self) -> usize {
        self.monitors.len()
    }

    /// 检查是否为多显示器环境
    pub fn is_multi_monitor(&self) -> bool {
        self.monitors.len() > 1
    }
}

impl Default for CoordinateSystem {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self { monitors: Vec::new() })
    }
}

// Tauri命令接口
use tauri::command;

/// 获取所有显示器信息
#[command]
pub async fn get_monitor_info() -> Result<Vec<MonitorInfo>, String> {
    let coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;
    
    Ok(coord_system.get_monitors().to_vec())
}

/// 获取虚拟屏幕边界
#[command]
pub async fn get_virtual_screen_bounds() -> Result<ScreenRegion, String> {
    let coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;
    
    coord_system.get_virtual_screen_bounds()
        .map_err(|e| format!("Failed to get virtual screen bounds: {}", e))
}

/// 根据坐标点获取显示器信息
#[command]
pub async fn get_monitor_at_point(x: i32, y: i32) -> Result<Option<MonitorInfo>, String> {
    let coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;
    
    Ok(coord_system.get_monitor_at_point(x, y).cloned())
}

/// 验证屏幕区域
#[command]
pub async fn validate_screen_region(region: ScreenRegion) -> Result<bool, String> {
    let coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;
    
    match coord_system.validate_region(&region) {
        Ok(()) => Ok(true),
        Err(_) => Ok(false),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_coordinate_conversion() {
        // 模拟显示器配置
        let monitors = vec![
            MonitorInfo {
                index: 0,
                name: "Primary".to_string(),
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
                scale_factor: 1.0,
                is_primary: true,
            },
            MonitorInfo {
                index: 1,
                name: "Secondary".to_string(),
                x: 1920,
                y: 0,
                width: 1920,
                height: 1080,
                scale_factor: 1.0,
                is_primary: false,
            },
        ];

        let coord_system = CoordinateSystem { monitors };

        // 测试全局坐标转换
        let global = GlobalCoordinate {
            x: 100,
            y: 100,
            monitor_index: Some(0),
        };

        let (rel_x, rel_y, monitor_idx) = coord_system.global_to_monitor_coordinate(&global).unwrap();
        assert_eq!(rel_x, 100);
        assert_eq!(rel_y, 100);
        assert_eq!(monitor_idx, 0);

        // 测试区域转换
        let region = ScreenRegion {
            x: 50,
            y: 50,
            width: 200,
            height: 150,
            monitor_index: 0,
        };

        let (x, y, w, h, idx) = coord_system.region_to_xcap_coordinates(&region).unwrap();
        assert_eq!(x, 50);
        assert_eq!(y, 50);
        assert_eq!(w, 200);
        assert_eq!(h, 150);
        assert_eq!(idx, 0);
    }

    #[test]
    fn test_monitor_detection() {
        let coord_system = CoordinateSystem::new();
        assert!(coord_system.is_ok());
    }
}
