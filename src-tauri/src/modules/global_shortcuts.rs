use tauri::AppHandle;
use tauri_plugin_global_shortcut::{Code, GlobalShortcutExt, Shortcut};

/// 注册全局ESC快捷键用于退出截图模式
pub fn register_global_esc_shortcut(app_handle: &AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    println!("[GLOBAL-SHORTCUT] 🎯 Registering global ESC shortcut for screenshot exit");
    
    // 创建ESC快捷键（不需要修饰键）
    let esc_shortcut = Shortcut::new(None, Code::Escape);
    
    // 注册全局快捷键
    app_handle.global_shortcut().register(esc_shortcut)?;
    
    println!("[GLOBAL-SHORTCUT] ✅ Global ESC shortcut registered successfully");
    Ok(())
}

/// 注销全局ESC快捷键
pub fn unregister_global_esc_shortcut(app_handle: &AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    println!("[GLOBAL-SHORTCUT] 🎯 Unregistering global ESC shortcut");
    
    let esc_shortcut = Shortcut::new(None, Code::Escape);
    app_handle.global_shortcut().unregister(esc_shortcut)?;
    
    println!("[GLOBAL-SHORTCUT] ✅ Global ESC shortcut unregistered successfully");
    Ok(())
}

/// 设置全局快捷键处理器
pub fn setup_global_shortcut_handler(app_handle: &AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    println!("[GLOBAL-SHORTCUT] 🎯 Setting up global shortcut handler");
    
    // 注意：在Tauri v2中，全局快捷键处理器需要在插件初始化时设置
    // 这个函数主要用于注册ESC快捷键
    register_global_esc_shortcut(app_handle)?;
    
    Ok(())
}

/// 检查ESC快捷键是否已注册
pub fn is_esc_shortcut_registered(app_handle: &AppHandle) -> bool {
    let esc_shortcut = Shortcut::new(None, Code::Escape);
    app_handle.global_shortcut().is_registered(esc_shortcut)
}
