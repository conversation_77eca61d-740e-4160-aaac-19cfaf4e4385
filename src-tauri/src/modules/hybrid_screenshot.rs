// 混合截图模块 - 结合平台特定API和全局快捷键的高级截图功能
use serde::{Deserialize, Serialize};
use tauri::{command, AppH<PERSON><PERSON>, Manager, Emitter};
use xcap::{Monitor, Window};
use anyhow::{Result, anyhow};
use log::{debug, info};

use super::coordinate::{CoordinateSystem, ScreenRegion, MonitorInfo};
use super::overlay::{OverlayType, create_modern_overlay};

/// 截图结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScreenshotResult {
    pub path: String,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub timestamp: u64,
    pub region: Option<ScreenRegion>,
}

/// 窗口信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowInfo {
    pub handle: u64,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub title: Option<String>,
    pub process_name: Option<String>,
}

/// 混合截图管理器
pub struct HybridScreenshotManager {
    coordinate_system: CoordinateSystem,
    active_overlay_id: Option<String>,
}

impl HybridScreenshotManager {
    pub fn new() -> Result<Self> {
        Ok(Self {
            coordinate_system: CoordinateSystem::new()?,
            active_overlay_id: None,
        })
    }

    /// 启动混合截图流程
    pub async fn start_hybrid_screenshot(&mut self, app_handle: AppHandle) -> Result<String> {
        // 刷新显示器信息
        self.coordinate_system.refresh()?;

        // 创建截图覆盖层
        let overlay_id = create_modern_overlay(app_handle.clone(), OverlayType::ScreenshotOverlay)
            .await
            .map_err(|e| anyhow!("Failed to create screenshot overlay: {}", e))?;

        self.active_overlay_id = Some(overlay_id.clone());

        // 发送显示器信息到前端
        let monitors = self.coordinate_system.get_monitors();
        app_handle.emit("monitors-info", monitors)
            .map_err(|e| anyhow!("Failed to emit monitors info: {}", e))?;

        println!("[HYBRID_SCREENSHOT] Started hybrid screenshot with overlay: {}", overlay_id);
        Ok(overlay_id)
    }

    /// 从覆盖层捕获区域
    pub async fn capture_region_from_overlay(&self, region: ScreenRegion) -> Result<ScreenshotResult> {
        // 验证区域
        self.coordinate_system.validate_region(&region)?;

        // 转换坐标
        let (x, y, width, height, monitor_index) = self.coordinate_system.region_to_xcap_coordinates(&region)?;

        // 获取目标显示器
        let monitors = Monitor::all()
            .map_err(|e| anyhow!("Failed to get monitors: {}", e))?;
        
        let target_monitor = monitors.get(monitor_index)
            .ok_or_else(|| anyhow!("Invalid monitor index: {}", monitor_index))?;

        // 执行截图 - 先捕获整个显示器，然后裁剪区域
        let full_image = target_monitor.capture_image()
            .map_err(|e| anyhow!("Failed to capture monitor: {}", e))?;

        // 裁剪到指定区域
        let image = image::imageops::crop_imm(&full_image, x as u32, y as u32, width, height).to_image();

        // 生成保存路径
        let timestamp = chrono::Utc::now().timestamp_millis() as u64;
        let filename = format!("screenshot_{}.png", timestamp);
        let save_path = self.generate_save_path(&filename)?;

        // 保存图像
        image.save(&save_path)
            .map_err(|e| anyhow!("Failed to save image: {}", e))?;

        Ok(ScreenshotResult {
            path: save_path,
            width: Some(image.width()),
            height: Some(image.height()),
            timestamp,
            region: Some(region),
        })
    }

    /// 生成保存路径
    fn generate_save_path(&self, filename: &str) -> Result<String> {
        let pictures_dir = dirs::picture_dir()
            .ok_or_else(|| anyhow!("Could not find pictures directory"))?;
        
        let mecap_dir = pictures_dir.join("Mecap");
        std::fs::create_dir_all(&mecap_dir)
            .map_err(|e| anyhow!("Failed to create Mecap directory: {}", e))?;

        Ok(mecap_dir.join(filename).to_string_lossy().to_string())
    }

    /// 关闭活动的覆盖层
    pub async fn close_active_overlay(&mut self, app_handle: AppHandle) -> Result<()> {
        if let Some(overlay_id) = &self.active_overlay_id {
            if let Some(window) = app_handle.get_webview_window(overlay_id) {
                window.close()
                    .map_err(|e| anyhow!("Failed to close overlay: {}", e))?;
            }
            self.active_overlay_id = None;
        }
        Ok(())
    }
}

// 全局管理器实例
lazy_static::lazy_static! {
    static ref HYBRID_MANAGER: std::sync::Mutex<Option<HybridScreenshotManager>> = 
        std::sync::Mutex::new(None);
}

/// 初始化混合截图管理器
fn ensure_manager() -> Result<()> {
    let mut manager = HYBRID_MANAGER.lock()
        .map_err(|e| anyhow!("Failed to lock manager: {}", e))?;
    
    if manager.is_none() {
        *manager = Some(HybridScreenshotManager::new()?);
    }
    Ok(())
}

// Tauri命令接口

/// 启动混合截图
#[command]
pub async fn start_hybrid_screenshot(app_handle: AppHandle) -> Result<String, String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例来避免跨await的锁持有
    let mut temp_manager = {
        let mut manager = HYBRID_MANAGER.lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(mgr) = manager.take() {
            mgr
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 执行操作
    let result = temp_manager.start_hybrid_screenshot(app_handle).await
        .map_err(|e| e.to_string());

    // 将管理器放回
    {
        let mut manager = HYBRID_MANAGER.lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;
        *manager = Some(temp_manager);
    }

    result
}

/// 从覆盖层捕获区域
#[command]
pub async fn capture_region_from_overlay(region: ScreenRegion) -> Result<ScreenshotResult, String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例
    let temp_manager = {
        let manager = HYBRID_MANAGER.lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(ref mgr) = manager.as_ref() {
            // 克隆必要的数据而不是整个管理器
            mgr.coordinate_system.clone()
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 使用临时管理器执行截图
    let temp_mgr = HybridScreenshotManager {
        coordinate_system: temp_manager,
        active_overlay_id: None,
    };

    temp_mgr.capture_region_from_overlay(region).await
        .map_err(|e| e.to_string())
}

/// 关闭混合截图覆盖层
#[command]
pub async fn close_hybrid_screenshot(app_handle: AppHandle) -> Result<(), String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例
    let mut temp_manager = {
        let mut manager = HYBRID_MANAGER.lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(mgr) = manager.take() {
            mgr
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 执行操作
    let result = temp_manager.close_active_overlay(app_handle).await
        .map_err(|e| e.to_string());

    // 将管理器放回
    {
        let mut manager = HYBRID_MANAGER.lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;
        *manager = Some(temp_manager);
    }

    result
}

/// 获取当前显示器信息
#[command]
pub async fn get_current_monitors() -> Result<Vec<MonitorInfo>, String> {
    // 直接创建新的坐标系统实例，避免锁的问题
    let mut coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;

    coord_system.refresh()
        .map_err(|e| format!("Failed to refresh monitors: {}", e))?;

    Ok(coord_system.get_monitors().to_vec())
}

// Windows平台特定的窗口检测实现
#[cfg(target_os = "windows")]
mod windows_detection {
    use super::*;
    use windows::Win32::UI::WindowsAndMessaging::{
        WindowFromPoint, GetWindowRect, GetWindowTextW, IsWindowVisible, GetWindow, GetWindowThreadProcessId
    };
    use windows::Win32::Foundation::{POINT, RECT, HWND, BOOL};
    use windows::Win32::System::Threading::{GetCurrentProcessId, OpenProcess, PROCESS_QUERY_INFORMATION};
    use windows::Win32::System::ProcessStatus::GetModuleBaseNameW;
    use windows::Win32::UI::WindowsAndMessaging::GW_OWNER;

    /// Windows平台高级窗口检测
    pub fn get_window_under_cursor(x: i32, y: i32) -> Option<WindowInfo> {
        trace!("[WIN_DETECT] Starting Windows window detection at ({}, {})", x, y);

        unsafe {
            let point = POINT { x, y };
            let hwnd = WindowFromPoint(point);

            if hwnd.0 == 0 {
                debug!("[WIN_DETECT] WindowFromPoint returned null handle");
                return None;
            }

            debug!("[WIN_DETECT] Found window handle: 0x{:X}", hwnd.0);

            // 检查窗口是否可见
            if IsWindowVisible(hwnd) == BOOL(0) {
                debug!("[WIN_DETECT] Window is not visible, skipping");
                return None;
            }

            trace!("[WIN_DETECT] Window is visible, proceeding with detection");

            // 获取顶层窗口（跳过子窗口）
            let top_level_hwnd = get_top_level_window(hwnd);
            if top_level_hwnd.0 != hwnd.0 {
                debug!("[WIN_DETECT] Found top-level window: 0x{:X} (was 0x{:X})", top_level_hwnd.0, hwnd.0);
            }

            // 获取窗口矩形
            let mut rect = RECT::default();
            if GetWindowRect(top_level_hwnd, &mut rect).is_err() {
                warn!("[WIN_DETECT] Failed to get window rectangle for handle 0x{:X}", top_level_hwnd.0);
                return None;
            }

            debug!("[WIN_DETECT] Window rect: ({}, {}) {}x{}", rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top);

            // 获取窗口标题
            let title = get_window_title(top_level_hwnd);
            debug!("[WIN_DETECT] Window title: {:?}", if title.is_empty() { None } else { Some(&title) });

            // 获取进程名
            let process_name = get_process_name(top_level_hwnd);
            debug!("[WIN_DETECT] Process name: {:?}", process_name);

            let window_info = WindowInfo {
                handle: top_level_hwnd.0 as u64,
                x: rect.left,
                y: rect.top,
                width: (rect.right - rect.left) as u32,
                height: (rect.bottom - rect.top) as u32,
                title: if title.is_empty() { None } else { Some(title) },
                process_name,
            };

            info!("[WIN_DETECT] Successfully detected window: handle=0x{:X}, size={}x{}, title={:?}, process={:?}",
                window_info.handle, window_info.width, window_info.height, window_info.title, window_info.process_name);

            Some(window_info)
        }
    }

    /// 获取顶层窗口
    unsafe fn get_top_level_window(mut hwnd: HWND) -> HWND {
        // 向上遍历找到顶层窗口
        loop {
            let owner = GetWindow(hwnd, GW_OWNER);
            if owner.0 == 0 {
                break;
            }
            hwnd = owner;
        }
        hwnd
    }

    /// 获取窗口标题
    unsafe fn get_window_title(hwnd: HWND) -> String {
        trace!("[WIN_TITLE] Getting window title for handle 0x{:X}", hwnd.0);

        let mut title_buffer = [0u16; 512];
        let title_len = GetWindowTextW(hwnd, &mut title_buffer);
        if title_len > 0 {
            let title = String::from_utf16_lossy(&title_buffer[..title_len as usize]);
            debug!("[WIN_TITLE] Retrieved title: '{}'", title);
            title
        } else {
            debug!("[WIN_TITLE] No title found for window 0x{:X}", hwnd.0);
            String::new()
        }
    }

    /// 获取进程名
    unsafe fn get_process_name(hwnd: HWND) -> Option<String> {
        trace!("[WIN_PROCESS] Getting process name for window 0x{:X}", hwnd.0);

        let mut process_id = 0u32;
        GetWindowThreadProcessId(hwnd, Some(&mut process_id));

        if process_id == 0 {
            debug!("[WIN_PROCESS] Failed to get process ID for window 0x{:X}", hwnd.0);
            return None;
        }

        debug!("[WIN_PROCESS] Process ID: {}", process_id);

        // 打开进程句柄
        let process_handle = OpenProcess(PROCESS_QUERY_INFORMATION, BOOL(0), process_id);
        if process_handle.is_err() {
            warn!("[WIN_PROCESS] Failed to open process handle for PID {}", process_id);
            return None;
        }

        let process_handle = process_handle.unwrap();

        // 获取进程模块名
        let mut module_name = [0u16; 256];
        let name_len = GetModuleBaseNameW(process_handle, None, &mut module_name);

        let _ = windows::Win32::Foundation::CloseHandle(process_handle);

        if name_len > 0 {
            let process_name = String::from_utf16_lossy(&module_name[..name_len as usize]);
            debug!("[WIN_PROCESS] Retrieved process name: '{}'", process_name);
            Some(process_name)
        } else {
            warn!("[WIN_PROCESS] Failed to get module name for PID {}", process_id);
            None
        }
    }

    /// 检查是否为系统窗口或特殊窗口
    pub fn is_system_window(window_info: &WindowInfo) -> bool {
        trace!("[WIN_SYSTEM_CHECK] Checking if window is system window: handle=0x{:X}", window_info.handle);

        if let Some(ref process_name) = window_info.process_name {
            debug!("[WIN_SYSTEM_CHECK] Process name: {}", process_name);

            let system_processes = [
                "dwm.exe",      // Desktop Window Manager
                "winlogon.exe", // Windows Logon
                "csrss.exe",    // Client Server Runtime Process
                "explorer.exe", // Windows Explorer (部分情况)
                "svchost.exe",  // Service Host
                "lsass.exe",    // Local Security Authority
                "wininit.exe",  // Windows Initialization
                "services.exe", // Service Control Manager
            ];

            let process_lower = process_name.to_lowercase();
            for &sys_proc in &system_processes {
                if process_lower.contains(&sys_proc.to_lowercase()) {
                    info!("[WIN_SYSTEM_CHECK] Identified as system window: process={}, matched={}", process_name, sys_proc);
                    return true;
                }
            }

            // 额外检查：无标题的系统窗口
            if window_info.title.is_none() || window_info.title.as_ref().unwrap().trim().is_empty() {
                debug!("[WIN_SYSTEM_CHECK] Window has no title, might be system window");
                // 对于某些已知的系统进程，即使没有明确匹配也认为是系统窗口
                if process_lower.contains("system") || process_lower.contains("registry") {
                    info!("[WIN_SYSTEM_CHECK] Identified as system window due to process name pattern: {}", process_name);
                    return true;
                }
            }

            debug!("[WIN_SYSTEM_CHECK] Not a system window: {}", process_name);
            false
        } else {
            warn!("[WIN_SYSTEM_CHECK] No process name available, assuming system window");
            true // 没有进程名的窗口通常是系统窗口
        }
    }

    /// 获取窗口详细信息
    pub fn get_enhanced_window_info(x: i32, y: i32) -> Option<EnhancedWindowInfo> {
        trace!("[WIN_ENHANCED] Starting enhanced window detection at ({}, {})", x, y);

        let basic_info = match get_window_under_cursor(x, y) {
            Some(info) => {
                debug!("[WIN_ENHANCED] Basic window info obtained");
                info
            }
            None => {
                debug!("[WIN_ENHANCED] No basic window info found");
                return None;
            }
        };

        let hwnd = HWND(basic_info.handle as isize);

        // 检查是否为系统窗口
        let is_system = is_system_window(&basic_info);
        debug!("[WIN_ENHANCED] System window check: {}", is_system);

        // 检查是否最大化
        let is_maximized = is_window_maximized(hwnd);
        debug!("[WIN_ENHANCED] Maximized check: {}", is_maximized);

        // 获取Z序
        let z_order = get_window_z_order(hwnd);
        debug!("[WIN_ENHANCED] Z-order: {}", z_order);

        let enhanced_info = EnhancedWindowInfo {
            basic: basic_info.clone(),
            is_system,
            is_maximized,
            z_order,
        };

        info!("[WIN_ENHANCED] Enhanced window info complete: handle=0x{:X}, is_system={}, is_maximized={}, z_order={}",
            basic_info.handle, is_system, is_maximized, z_order);

        Some(enhanced_info)
    }

    /// 检查窗口是否最大化
    unsafe fn is_window_maximized(hwnd: HWND) -> bool {
        use windows::Win32::UI::WindowsAndMessaging::{GetWindowPlacement, WINDOWPLACEMENT, SW_SHOWMAXIMIZED};

        trace!("[WIN_MAXIMIZED] Checking if window 0x{:X} is maximized", hwnd.0);

        let mut placement = WINDOWPLACEMENT::default();
        placement.length = std::mem::size_of::<WINDOWPLACEMENT>() as u32;

        if GetWindowPlacement(hwnd, &mut placement).is_ok() {
            let is_maximized = placement.showCmd == SW_SHOWMAXIMIZED.0 as u32;
            debug!("[WIN_MAXIMIZED] Window 0x{:X} maximized status: {}", hwnd.0, is_maximized);
            is_maximized
        } else {
            warn!("[WIN_MAXIMIZED] Failed to get window placement for 0x{:X}", hwnd.0);
            false
        }
    }

    /// 获取窗口Z序
    unsafe fn get_window_z_order(hwnd: HWND) -> i32 {
        use windows::Win32::UI::WindowsAndMessaging::{GetWindow, GW_HWNDPREV};

        trace!("[WIN_ZORDER] Calculating Z-order for window 0x{:X}", hwnd.0);

        let mut z_order = 0;
        let mut current = hwnd;

        // 向前遍历计算Z序
        while current.0 != 0 {
            current = GetWindow(current, GW_HWNDPREV);
            if current.0 != 0 {
                z_order += 1;
            }
        }

        debug!("[WIN_ZORDER] Window 0x{:X} Z-order: {}", hwnd.0, z_order);
        z_order
    }

    /// 增强的窗口信息
    #[derive(Debug, Clone)]
    pub struct EnhancedWindowInfo {
        pub basic: WindowInfo,
        pub is_system: bool,
        pub is_maximized: bool,
        pub z_order: i32,
    }
}

// macOS平台特定的窗口检测实现
#[cfg(target_os = "macos")]
mod macos_detection {
    use super::*;
    use log::{debug, info, warn, error};
    use core_graphics::window::{
        CGWindowListCopyWindowInfo, kCGWindowListOptionOnScreenOnly,
        kCGNullWindowID
    };
    use core_foundation::{
        base::TCFType,
        dictionary::{CFDictionary, CFDictionaryRef},
        number::{CFNumber, CFNumberRef},
        string::{CFString, CFStringRef},
        array::{CFArray, CFArrayRef},
    };

    /// macOS平台窗口检测实现
    pub fn get_window_under_cursor(x: f64, y: f64) -> Option<WindowInfo> {
        let start_time = std::time::Instant::now();
        log::trace!("[MACOS] Starting window detection at coordinates ({}, {})", x, y);

        // 检查屏幕录制权限
        if !check_screen_recording_permission() {
            warn!("[MACOS] Screen recording permission not granted");
            return None;
        }

        // 获取窗口列表（优化：在一次检测中复用窗口列表）
        let window_list = match get_window_list() {
            Some(list) => list,
            None => {
                error!("[MACOS] Failed to get window list");
                return None;
            }
        };

        log::trace!("[MACOS] Retrieved {} windows from system", window_list.len());

        // 查找包含指定坐标的窗口（按Z-order排序，最上层优先）
        let mut candidates = Vec::new();

        for (i, window_info) in window_list.iter().enumerate() {
            if point_in_window(x, y, window_info) {
                log::trace!("[MACOS] Window {} contains point ({}, {}): {:?} - {:?} ({}x{} at {},{}) layer={}",
                    i, x, y, window_info.process_name, window_info.title,
                    window_info.width, window_info.height, window_info.x, window_info.y, window_info.handle);
                candidates.push(window_info.clone());
            }
        }

        let detection_time = start_time.elapsed();

        if candidates.is_empty() {
            log::trace!("[MACOS] No window found at coordinates ({}, {}) in {:.2}ms", x, y, detection_time.as_millis());
            None
        } else {
            // 使用智能选择算法，考虑应用程序焦点状态（传递窗口列表避免重复获取）
            if let Some(selected_window) = select_best_window_with_focus(&candidates, &window_list) {
                log::trace!("[MACOS] Found {} candidate windows at ({}, {}), selected: {} ({}) in {:.2}ms",
                    candidates.len(), x, y,
                    selected_window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                    selected_window.process_name.as_ref().unwrap_or(&"Unknown".to_string()),
                    detection_time.as_millis());
                Some(selected_window)
            } else {
                log::trace!("[MACOS] No suitable window found at coordinates ({}, {}) in {:.2}ms", x, y, detection_time.as_millis());
                None
            }
        }
    }

    /// 检查屏幕录制权限
    pub fn check_screen_recording_permission() -> bool {
        unsafe {
            // 尝试获取窗口列表来检查权限
            let window_list_info = CGWindowListCopyWindowInfo(
                kCGWindowListOptionOnScreenOnly,
                kCGNullWindowID,
            );

            if window_list_info.is_null() {
                info!("[MACOS] Screen recording permission check failed - window list is null");
                return false;
            }

            let window_array = CFArray::<*const core_foundation::base::CFTypeRef>::wrap_under_get_rule(window_list_info as CFArrayRef);
            let window_count = window_array.len();

            // 如果能获取到窗口列表且数量合理，说明有权限
            if window_count > 0 {
                log::trace!("[MACOS] Screen recording permission check passed - found {} windows", window_count);
                true
            } else {
                info!("[MACOS] Screen recording permission check failed - no windows found");
                false
            }
        }
    }

    /// 请求屏幕录制权限（引导用户到系统设置）
    pub fn request_screen_recording_permission() -> Result<(), String> {
        info!("[MACOS] Requesting screen recording permission");

        // 在macOS中，我们不能直接请求权限，只能引导用户到系统设置
        // 这里可以显示一个对话框或通知，告诉用户如何授权

        // 尝试打开系统偏好设置的隐私页面
        use std::process::Command;

        let result = Command::new("open")
            .arg("x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture")
            .output();

        match result {
            Ok(_) => {
                info!("[MACOS] Successfully opened System Preferences for screen recording permission");
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("Failed to open System Preferences: {}", e);
                info!("[MACOS] {}", error_msg);
                Err(error_msg)
            }
        }
    }

    /// 获取系统窗口列表
    fn get_window_list() -> Option<Vec<WindowInfo>> {
        unsafe {
            log::trace!("[MACOS] Starting to get window list from system");

            // 获取屏幕上所有可见窗口的信息
            let window_list_info = CGWindowListCopyWindowInfo(
                kCGWindowListOptionOnScreenOnly,
                kCGNullWindowID,
            );

            if window_list_info.is_null() {
                error!("[MACOS] Failed to get window list from system - CGWindowListCopyWindowInfo returned null");
                return None;
            }

            let window_array = CFArray::<CFDictionary>::wrap_under_get_rule(window_list_info as CFArrayRef);
            let total_windows = window_array.len();
            log::trace!("[MACOS] Retrieved {} total windows from system", total_windows);

            let mut windows = Vec::new();
            let mut filtered_count = 0;

            for i in 0..window_array.len() {
                if let Some(window_dict) = window_array.get(i) {
                    log::trace!("[MACOS] Processing window {}/{}", i + 1, total_windows);

                    if let Some(window_info) = parse_window_info(&window_dict) {
                        log::trace!("[MACOS] Parsed window: {:?} ({}x{} at {},{}) layer={}",
                            window_info.process_name, window_info.width, window_info.height,
                            window_info.x, window_info.y, window_info.handle);

                        // 过滤掉系统窗口和无效窗口
                        if is_valid_user_window(&window_info) {
                            windows.push(window_info);
                        } else {
                            filtered_count += 1;
                        }
                    } else {
                        debug!("[MACOS] Failed to parse window {}", i + 1);
                    }
                }
            }

            log::trace!("[MACOS] Filtered out {} windows, keeping {} valid windows", filtered_count, windows.len());

            // 按Z-order排序（layer值越大越在上层）
            windows.sort_by(|a, b| {
                let layer_a = a.handle; // 使用handle存储layer信息
                let layer_b = b.handle;
                layer_b.cmp(&layer_a) // 降序排列，最上层在前
            });

            log::trace!("[MACOS] Final window list has {} windows, sorted by Z-order", windows.len());
            // 只在TRACE级别显示完整窗口列表，减少日志噪音
            for (i, window) in windows.iter().enumerate() {
                log::trace!("[MACOS] Window {}: {:?} - {:?} (layer={})",
                    i, window.process_name, window.title, window.handle);
            }

            Some(windows)
        }
    }

    /// 解析窗口信息字典
    fn parse_window_info(window_dict: &CFDictionary) -> Option<WindowInfo> {
        unsafe {
            // 获取窗口ID
            let _window_number = get_dict_number(window_dict, "kCGWindowNumber")?;

            // 获取窗口边界
            let bounds_dict = get_dict_dict(window_dict, "kCGWindowBounds")?;
            let x = get_dict_number(&bounds_dict, "X")? as i32;
            let y = get_dict_number(&bounds_dict, "Y")? as i32;
            let width = get_dict_number(&bounds_dict, "Width")? as u32;
            let height = get_dict_number(&bounds_dict, "Height")? as u32;

            // 获取窗口标题
            let title = get_dict_string(window_dict, "kCGWindowName");

            // 获取应用名称
            let process_name = get_dict_string(window_dict, "kCGWindowOwnerName");

            // 获取进程ID
            let _process_id = get_dict_number(window_dict, "kCGWindowOwnerPID").unwrap_or(0.0);

            // 获取窗口层级
            let layer = get_dict_number(window_dict, "kCGWindowLayer").unwrap_or(0.0);

            Some(WindowInfo {
                handle: layer as u64, // 使用layer作为handle
                x,
                y,
                width,
                height,
                title,
                process_name,
            })
        }
    }

    /// 辅助函数：从字典中获取数字值
    unsafe fn get_dict_number(dict: &CFDictionary, key: &str) -> Option<f64> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            let cf_number = CFNumber::wrap_under_get_rule(*cf_value as CFNumberRef);
            cf_number.to_f64()
        } else {
            None
        }
    }

    /// 辅助函数：从字典中获取字符串值
    unsafe fn get_dict_string(dict: &CFDictionary, key: &str) -> Option<String> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            let cf_string = CFString::wrap_under_get_rule(*cf_value as CFStringRef);
            Some(cf_string.to_string())
        } else {
            None
        }
    }

    /// 辅助函数：从字典中获取字典值
    unsafe fn get_dict_dict(dict: &CFDictionary, key: &str) -> Option<CFDictionary> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            Some(CFDictionary::wrap_under_get_rule(*cf_value as CFDictionaryRef))
        } else {
            None
        }
    }

    /// 检查坐标点是否在窗口内
    fn point_in_window(x: f64, y: f64, window: &WindowInfo) -> bool {
        x >= window.x as f64
            && x <= (window.x + window.width as i32) as f64
            && y >= window.y as f64
            && y <= (window.y + window.height as i32) as f64
    }

    /// 检查是否为有效的用户窗口
    fn is_valid_user_window(window: &WindowInfo) -> bool {
        // 只在DEBUG级别记录详细过滤信息，减少日志噪音
        log::trace!("[MACOS_FILTER] Checking window: title={:?}, process={:?}, size={}x{}",
            window.title, window.process_name, window.width, window.height);

        // 使用静态变量跟踪已过滤的Mecap窗口，避免重复日志
        use std::sync::Mutex;
        static MECAP_FILTER_LOGGED: Mutex<bool> = Mutex::new(false);

        // 过滤掉无效尺寸的窗口
        if window.width < 10 || window.height < 10 {
            log::trace!("[MACOS_FILTER] Filtered out: window too small");
            return false;
        }

        // 过滤掉系统窗口和Mecap自身的窗口
        if let Some(ref process_name) = window.process_name {
            let system_processes = [
                "Window Server",
                "Dock",
                "SystemUIServer",
                "ControlCenter",
                "NotificationCenter",
                "Spotlight",
                "loginwindow",
            ];

            // 检查系统进程
            for &sys_proc in &system_processes {
                if process_name.contains(sys_proc) {
                    log::trace!("[MACOS_FILTER] Filtered out: system process {}", sys_proc);
                    return false;
                }
            }

            // 特别过滤掉Mecap自身的窗口（不区分大小写）
            let process_lower = process_name.to_lowercase();
            if process_lower.contains("mecap") {
                // 使用智能日志记录，避免重复的Mecap过滤日志
                let mut logged = MECAP_FILTER_LOGGED.lock().unwrap();
                if !*logged {
                    log::debug!("[MACOS_FILTER] 🚫 Filtering out Mecap overlay windows (process={})", process_name);
                    *logged = true;
                }
                return false;
            }
        }

        // 额外检查窗口标题，过滤掉Mecap的覆盖层窗口
        if let Some(ref title) = window.title {
            let title_lower = title.to_lowercase();
            if title_lower.contains("mecap") && (title_lower.contains("highlight") || title_lower.contains("overlay")) {
                log::trace!("[MACOS_FILTER] Filtered out: Mecap overlay window (title={})", title);
                return false;
            }
        }

        // 检查窗口层级 - 过滤掉太低层级的窗口
        let layer = window.handle as i32; // handle存储的是layer
        if layer < 0 {
            log::trace!("[MACOS_FILTER] Filtered out: negative layer {}", layer);
            return false;
        }

        log::trace!("[MACOS_FILTER] ✅ Window passed all filters: process={:?}, title={:?}, size={}x{}",
            window.process_name, window.title, window.width, window.height);
        true
    }

    /// 获取当前聚焦的应用程序名称 - 优化版本，复用窗口列表
    fn get_focused_application_from_list(window_list: &[WindowInfo]) -> Option<String> {
        // 通过分析窗口Z-order来推断当前聚焦的应用程序
        // 获取最上层的非系统窗口作为聚焦应用程序的指示
        for window in window_list {
            // 跳过系统窗口和菜单栏窗口
            if let Some(ref process_name) = window.process_name {
                let system_processes = [
                    "Window Server", "Dock", "SystemUIServer", "ControlCenter",
                    "NotificationCenter", "Spotlight", "loginwindow", "程序坞",
                    "控制中心", "聚焦", "TextInputMenuAgent"
                ];

                let is_system = system_processes.iter().any(|&sys_proc| process_name.contains(sys_proc));

                // 如果是用户应用程序窗口且尺寸合理，认为是聚焦的应用程序
                if !is_system && window.width > 100 && window.height > 100 {
                    log::trace!("[MACOS_FOCUS] Inferred focused application from top window: {}", process_name);
                    return Some(process_name.clone());
                }
            }
        }

        log::debug!("[MACOS_FOCUS] Could not determine focused application");
        None
    }

    /// 智能窗口选择 - 考虑应用程序焦点状态（优化版本，复用窗口列表）
    fn select_best_window_with_focus(candidates: &[WindowInfo], window_list: &[WindowInfo]) -> Option<WindowInfo> {
        if candidates.is_empty() {
            return None;
        }

        // 获取当前聚焦的应用程序（复用已获取的窗口列表，避免重复调用）
        let focused_app = get_focused_application_from_list(window_list);

        // 使用静态变量跟踪上次的聚焦应用程序，只在变化时记录日志
        use std::sync::Mutex;
        static LAST_FOCUSED_APP: Mutex<Option<String>> = Mutex::new(None);

        if let Some(ref focused_name) = focused_app {
            let mut last_focused = LAST_FOCUSED_APP.lock().unwrap();
            let should_log = last_focused.as_ref() != Some(focused_name);
            if should_log {
                log::debug!("[MACOS_FOCUS] 🔄 Focus changed to: {}", focused_name);
                *last_focused = Some(focused_name.clone());
            }
            log::trace!("[MACOS_FOCUS] Looking for windows from focused app: {}", focused_name);

            // 首先尝试找到聚焦应用程序的窗口
            for candidate in candidates {
                if let Some(ref process_name) = candidate.process_name {
                    // 检查进程名是否匹配聚焦的应用程序
                    if process_name.to_lowercase().contains(&focused_name.to_lowercase()) ||
                       focused_name.to_lowercase().contains(&process_name.to_lowercase()) {
                        log::info!("[MACOS_FOCUS] ✅ Selected window from focused app: {} ({})",
                            candidate.title.as_ref().unwrap_or(&"Untitled".to_string()), process_name);
                        return Some(candidate.clone());
                    }
                }
            }

            log::debug!("[MACOS_FOCUS] No windows found from focused app, falling back to layer-based selection");
        }

        // 如果没有找到聚焦应用程序的窗口，回退到原有的层级选择
        let selected = candidates.first().cloned();
        if let Some(ref window) = selected {
            log::info!("[MACOS_FOCUS] Selected window by layer: {} ({})",
                window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                window.process_name.as_ref().unwrap_or(&"Unknown".to_string()));
        }
        selected
    }
}

// Linux平台特定的窗口检测实现
#[cfg(target_os = "linux")]
mod linux_detection {
    use super::*;
    
    /// Linux平台窗口检测（占位实现）
    pub fn get_window_under_cursor(x: i16, y: i16) -> Option<WindowInfo> {
        // TODO: 实现Linux窗口检测
        // 使用x11rb或检测Wayland环境
        println!("[LINUX] Window detection not yet implemented for coordinates ({}, {})", x, y);
        None
    }
}

/// Windows平台增强窗口检测
#[cfg(target_os = "windows")]
#[command]
pub async fn detect_window_enhanced_windows(x: i32, y: i32) -> Result<Option<windows_detection::EnhancedWindowInfo>, String> {
    info!("[WIN_ENHANCED_CMD] Windows enhanced detection command called at ({}, {})", x, y);

    match windows_detection::get_enhanced_window_info(x, y) {
        Some(enhanced_info) => {
            info!("[WIN_ENHANCED_CMD] Enhanced detection successful: handle=0x{:X}, title={:?}, process={:?}",
                enhanced_info.basic.handle, enhanced_info.basic.title, enhanced_info.basic.process_name);
            Ok(Some(enhanced_info))
        }
        None => {
            debug!("[WIN_ENHANCED_CMD] No window found at coordinates ({}, {})", x, y);
            Ok(None)
        }
    }
}

/// macOS权限检查命令
#[cfg(target_os = "macos")]
#[command]
pub async fn check_macos_permissions() -> Result<bool, String> {
    info!("[MACOS_PERM] Checking macOS screen recording permissions");
    let has_permission = macos_detection::check_screen_recording_permission();
    info!("[MACOS_PERM] Permission check result: {}", has_permission);
    Ok(has_permission)
}

/// macOS权限请求命令
#[cfg(target_os = "macos")]
#[command]
pub async fn request_macos_permissions() -> Result<(), String> {
    info!("[MACOS_PERM] Requesting macOS screen recording permissions");
    macos_detection::request_screen_recording_permission()
}

/// 跨平台智能窗口检测（优先使用增强版本）
#[command]
pub async fn detect_window_smart(x: i32, y: i32) -> Result<Option<WindowInfo>, String> {
    log::trace!("[SMART_DETECT] Starting smart window detection at coordinates ({}, {})", x, y);

    #[cfg(target_os = "windows")]
    {
        debug!("[SMART_DETECT] Using Windows enhanced detection");
        match windows_detection::get_enhanced_window_info(x, y) {
            Some(enhanced) => {
                info!("[SMART_DETECT] Enhanced window info retrieved: handle={}, title={:?}, process={:?}, is_system={}, is_maximized={}, z_order={}",
                    enhanced.basic.handle,
                    enhanced.basic.title,
                    enhanced.basic.process_name,
                    enhanced.is_system,
                    enhanced.is_maximized,
                    enhanced.z_order
                );

                // 过滤系统窗口
                if enhanced.is_system {
                    info!("[SMART_DETECT] Filtering out system window: process={:?}", enhanced.basic.process_name);
                    Ok(None)
                } else {
                    info!("[SMART_DETECT] Valid user window detected: {}x{} at ({}, {})",
                        enhanced.basic.width, enhanced.basic.height, enhanced.basic.x, enhanced.basic.y);
                    Ok(Some(enhanced.basic))
                }
            }
            None => {
                debug!("[SMART_DETECT] No window found at coordinates ({}, {})", x, y);
                Ok(None)
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        log::trace!("[SMART_DETECT] Using macOS detection (placeholder)");
        let result = macos_detection::get_window_under_cursor(x as f64, y as f64);
        if result.is_some() {
            log::trace!("[SMART_DETECT] macOS window detected");
        } else {
            debug!("[SMART_DETECT] No macOS window found");
        }
        Ok(result)
    }

    #[cfg(target_os = "linux")]
    {
        info!("[SMART_DETECT] Using Linux detection (placeholder)");
        let result = linux_detection::get_window_under_cursor(x as i16, y as i16);
        if result.is_some() {
            info!("[SMART_DETECT] Linux window detected");
        } else {
            debug!("[SMART_DETECT] No Linux window found");
        }
        Ok(result)
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        error!("[SMART_DETECT] Unsupported platform for window detection");
        Err("Window detection not supported on this platform".to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hybrid_screenshot_manager() {
        let manager = HybridScreenshotManager::new();
        assert!(manager.is_ok());
    }

    #[test]
    fn test_window_detection_interface() {
        // 基本接口测试
        #[cfg(target_os = "windows")]
        {
            let result = windows_detection::get_window_under_cursor(100, 100);
            // 结果可能为None（如果没有窗口），但不应该panic
            println!("Windows detection result: {:?}", result);
        }
    }
}

/// 捕获选中的窗口截图
#[command]
pub async fn capture_selected_window(window_info: WindowInfo, save_path: Option<String>) -> Result<ScreenshotResult, String> {
    info!("[WINDOW_CAPTURE] Starting window capture for window: handle={}, title={:?}",
        window_info.handle, window_info.title);

    let start_time = std::time::Instant::now();

    // 获取所有窗口
    let windows = Window::all().map_err(|e| {
        let error_msg = format!("Failed to get window list: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    // 查找目标窗口
    let target_window = windows.into_iter().find(|w| {
        match w.id() {
            Ok(id) => id as u64 == window_info.handle,
            Err(_) => false,
        }
    });

    let window = match target_window {
        Some(w) => w,
        None => {
            let error_msg = format!("Window with handle {} not found", window_info.handle);
            log::error!("[WINDOW_CAPTURE] {}", error_msg);
            return Err(error_msg);
        }
    };

    // 检查窗口是否最小化
    if let Ok(is_minimized) = window.is_minimized() {
        if is_minimized {
            let error_msg = "Cannot capture minimized window";
            log::warn!("[WINDOW_CAPTURE] {}", error_msg);
            return Err(error_msg.to_string());
        }
    }

    // 获取窗口信息用于日志
    let window_title = window.title().unwrap_or_else(|_| "Unknown".to_string());
    let window_width = window.width().unwrap_or(0);
    let window_height = window.height().unwrap_or(0);

    info!("[WINDOW_CAPTURE] Capturing window: '{}' ({}x{})",
        window_title, window_width, window_height);

    // 执行窗口截图
    let image = window.capture_image().map_err(|e| {
        let error_msg = format!("Failed to capture window image: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    let capture_time = start_time.elapsed().as_millis() as u64;
    info!("[WINDOW_CAPTURE] Window captured in {}ms", capture_time);

    // 生成保存路径
    let timestamp = chrono::Utc::now().timestamp_millis();
    let save_path = save_path.unwrap_or_else(|| {
        let safe_title = window_title.chars()
            .filter(|c| c.is_alphanumeric() || *c == ' ' || *c == '-' || *c == '_')
            .collect::<String>()
            .replace(' ', "_");

        format!("mecap_window_{}_{}.png", safe_title, timestamp)
    });

    // 保存图像
    image.save(&save_path).map_err(|e| {
        let error_msg = format!("Failed to save window screenshot: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    info!("[WINDOW_CAPTURE] Window screenshot saved to: {}", save_path);

    // 创建截图结果
    let result = ScreenshotResult {
        path: save_path,
        width: Some(image.width()),
        height: Some(image.height()),
        timestamp: timestamp as u64,
        region: Some(ScreenRegion {
            x: window_info.x,
            y: window_info.y,
            width: window_info.width,
            height: window_info.height,
        }),
    };

    Ok(result)
}
