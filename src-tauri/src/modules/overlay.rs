// 覆盖层模块 - 专门处理覆盖层窗口管理和相关功能
use serde::{Deserialize, Serialize};
use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewWindowBuilder, WebviewUrl, Emitter};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

/// 前端日志接收命令
#[command]
pub async fn log_frontend_message(level: String, message: String, data: Option<String>) -> Result<(), String> {
    let log_prefix = match level.as_str() {
        "ERROR" => "[FRONTEND-ERROR]",
        "WARN" => "[FRONTEND-WARN]",
        "INFO" => "[FRONTEND-INFO]",
        "DEBUG" => "[FRONTEND-DEBUG]",
        _ => "[FRONTEND]",
    };

    if let Some(data) = data {
        println!("{} {} | Data: {}", log_prefix, message, data);
    } else {
        println!("{} {}", log_prefix, message);
    }

    Ok(())
}

// 覆盖层管理器 - 实时创建模式

// 新的模块化覆盖层功能

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OverlayInfo {
    pub id: String,
    pub window_type: OverlayType,
    pub created_at: u64,
    pub mode: OverlayMode,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverlayType {
    ScreenshotOverlay,
    PinnedImage,
    RegionSelector,
    WindowHighlight,
    QuickToolbar,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverlayMode {
    FullTransparent,    // 完全透明模式
    SemiTransparent,    // 半透明模式（Linux降级）
    Opaque,            // 不透明模式（最大兼容性）
}

// 全局覆盖层管理器
lazy_static::lazy_static! {
    static ref OVERLAY_MANAGER: Arc<Mutex<HashMap<String, OverlayInfo>>> = Arc::new(Mutex::new(HashMap::new()));
}

/// 创建现代化覆盖层窗口 - Phase 1核心功能
#[command]
pub async fn create_modern_overlay(app_handle: AppHandle, overlay_type: OverlayType) -> Result<String, String> {
    println!("[OVERLAY] Creating modern overlay: {:?}", overlay_type);
    
    let overlay_id = format!("modern_overlay_{}", chrono::Utc::now().timestamp_millis());
    
    // 检测系统能力并选择最佳模式
    let overlay_mode = detect_best_overlay_mode().await;
    
    // 根据覆盖层类型选择HTML页面
    let html_page = match overlay_type {
        OverlayType::ScreenshotOverlay => "overlay-screenshot.html",
        OverlayType::RegionSelector => "overlay-region-selector.html",
        OverlayType::WindowHighlight => "overlay-window-highlight.html",
        OverlayType::PinnedImage => "overlay-pinned-image.html",
        OverlayType::QuickToolbar => "overlay-quick-toolbar.html",
    };
    
    // 创建覆盖层窗口
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &overlay_id,
        WebviewUrl::App(html_page.into())
    )
    .title("Mecap Modern Overlay")
    .inner_size(1920.0, 1080.0)
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true);
    
    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        OverlayMode::SemiTransparent => {
            window_builder = window_builder.transparent(false);
        }
        OverlayMode::Opaque => {
            window_builder = window_builder.transparent(false);
        }
    }
    
    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }
            
            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;
            
            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: overlay_id.clone(),
                window_type: overlay_type.clone(),
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(overlay_id.clone(), overlay_info);
            }

            let success_msg = format!(
                "Modern overlay created successfully:\n- ID: {}\n- Type: {:?}\n- Mode: {:?}",
                overlay_id, overlay_type, overlay_mode
            );
            
            println!("[OVERLAY] {}", success_msg);
            Ok(overlay_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create modern overlay: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 检测最佳覆盖层模式
async fn detect_best_overlay_mode() -> OverlayMode {
    #[cfg(target_os = "linux")]
    {
        use std::process::Command;
        
        // 检测合成器支持
        let compositors = ["mutter", "kwin_x11", "kwin_wayland", "compiz", "xfwm4"];
        for compositor in &compositors {
            if let Ok(output) = Command::new("pgrep").arg(compositor).output() {
                if output.status.success() && !output.stdout.is_empty() {
                    return OverlayMode::FullTransparent;
                }
            }
        }
        
        // 检测Wayland
        if std::env::var("WAYLAND_DISPLAY").is_ok() {
            return OverlayMode::SemiTransparent;
        }
        
        // 降级到半透明模式
        OverlayMode::SemiTransparent
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        // 为了确保键盘事件能被捕获，使用半透明模式
        // 完全透明的窗口在macOS上可能无法接收键盘事件
        OverlayMode::SemiTransparent
    }
}

/// 应用覆盖层模式特定的样式
async fn apply_overlay_mode_styles(window: &tauri::WebviewWindow, mode: &OverlayMode) -> Result<(), String> {
    let css = match mode {
        OverlayMode::FullTransparent => {
            // 完全透明模式 - 使用真正的透明度
            r#"
                document.body.style.setProperty('background-color', 'transparent', 'important');
                document.documentElement.style.setProperty('background-color', 'transparent', 'important');

                // 额外确保透明度
                document.body.style.setProperty('background', 'transparent', 'important');
                document.documentElement.style.setProperty('background', 'transparent', 'important');

                // 调试信息
                console.log('[OVERLAY] ✅ Applied full transparent mode with setProperty');
                console.log('[OVERLAY] Body background:', window.getComputedStyle(document.body).backgroundColor);
                console.log('[OVERLAY] HTML background:', window.getComputedStyle(document.documentElement).backgroundColor);
            "#
        }
        OverlayMode::SemiTransparent => {
            // 半透明模式 - 使用极低透明度以确保能接收键盘事件
            r#"
                document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.01)', 'important');
                document.documentElement.style.setProperty('background-color', 'rgba(0, 0, 0, 0.01)', 'important');

                // 确保窗口能接收键盘事件
                document.body.style.setProperty('pointer-events', 'auto', 'important');
                document.documentElement.style.setProperty('pointer-events', 'auto', 'important');

                console.log('[OVERLAY] ✅ Semi-transparent mode with keyboard event support (alpha=0.01)');
            "#
        }
        OverlayMode::Opaque => {
            // 不透明模式 - 使用深色背景，但仍保持一定透明度以便窗口检测
            r#"
                document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.25)', 'important');
                document.documentElement.style.setProperty('background-color', 'rgba(0, 0, 0, 0.25)', 'important');
                console.log('[OVERLAY] Applied opaque mode with window detection optimization: rgba(0, 0, 0, 0.25)');
            "#
        }
    };
    
    // 尝试执行CSS，如果失败则等待并重试
    println!("[OVERLAY] 🔍 Attempting to apply CSS styles...");

    for attempt in 1..=3 {
        match window.eval(css) {
            Ok(_) => {
                println!("[OVERLAY] 🔍 CSS styles applied successfully on attempt {}", attempt);
                return Ok(());
            }
            Err(e) => {
                println!("[OVERLAY] 🔍 CSS application failed on attempt {}: {}", attempt, e);
                if attempt < 3 {
                    println!("[OVERLAY] 🔍 Waiting 200ms before retry...");
                    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
                } else {
                    return Err(format!("Failed to apply overlay styles after 3 attempts: {}", e));
                }
            }
        }
    }

    Ok(())
}

/// 测试Tauri连接的ping命令
#[command]
pub async fn ping_test() -> Result<String, String> {
    println!("[OVERLAY] 🏓 Ping test received from frontend");
    Ok("pong".to_string())
}

/// 关闭覆盖层窗口（新版本）
#[command]
pub async fn close_overlay_new(app_handle: AppHandle, overlay_id: String) -> Result<(), String> {
    println!("[OVERLAY] Closing overlay: {}", overlay_id);
    
    // 获取窗口并关闭
    if let Some(window) = app_handle.get_webview_window(&overlay_id) {
        if let Err(e) = window.close() {
            return Err(format!("Failed to close overlay window: {}", e));
        }
    }
    
    // 从管理器中移除
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.remove(&overlay_id);
    }
    
    println!("[OVERLAY] Overlay closed successfully: {}", overlay_id);
    Ok(())
}

/// 获取所有活动的覆盖层（新版本）
#[command]
pub async fn list_active_overlays_new() -> Result<Vec<OverlayInfo>, String> {
    match OVERLAY_MANAGER.lock() {
        Ok(manager) => {
            let overlays: Vec<OverlayInfo> = manager.values().cloned().collect();
            Ok(overlays)
        }
        Err(e) => Err(format!("Failed to access overlay manager: {}", e))
    }
}

/// 更新覆盖层属性
#[command]
pub async fn update_overlay_properties(
    app_handle: AppHandle,
    overlay_id: String,
    properties: OverlayProperties
) -> Result<(), String> {
    println!("[OVERLAY] Updating overlay properties: {}", overlay_id);
    
    if let Some(window) = app_handle.get_webview_window(&overlay_id) {
        // 应用新属性
        if let Some(opacity) = properties.opacity {
            let css = format!(
                "document.body.style.opacity = '{}'; console.log('Updated opacity to {}');",
                opacity, opacity
            );
            window.eval(&css).map_err(|e| format!("Failed to update opacity: {}", e))?;
        }
        
        if let Some(visible) = properties.visible {
            if visible {
                window.show().map_err(|e| format!("Failed to show overlay: {}", e))?;
            } else {
                window.hide().map_err(|e| format!("Failed to hide overlay: {}", e))?;
            }
        }
        
        println!("[OVERLAY] Overlay properties updated successfully");
        Ok(())
    } else {
        Err(format!("Overlay not found: {}", overlay_id))
    }
}

#[derive(Serialize, Deserialize, Debug)]
pub struct OverlayProperties {
    pub opacity: Option<f64>,
    pub visible: Option<bool>,
    pub always_on_top: Option<bool>,
}

/// 获取覆盖层性能统计
#[command]
pub async fn get_overlay_performance_stats() -> Result<String, String> {
    let mut stats = Vec::new();
    stats.push("=== Overlay Performance Statistics ===".to_string());

    if let Ok(manager) = OVERLAY_MANAGER.lock() {
        stats.push(format!("Active overlays: {}", manager.len()));

        for (id, info) in manager.iter() {
            let age_ms = chrono::Utc::now().timestamp_millis() as u64 - info.created_at;
            stats.push(format!(
                "- {}: {:?} mode, age {}ms",
                id, info.mode, age_ms
            ));
        }
    }

    let report = stats.join("\n");
    println!("[OVERLAY] Performance stats: {}", report);
    Ok(report)
}

/// 全屏覆盖层管理器 - Phase 1核心功能
#[command]
pub async fn create_fullscreen_overlay_manager(app_handle: AppHandle) -> Result<String, String> {
    println!("[OVERLAY] Creating fullscreen overlay manager");

    let manager_id = format!("overlay_manager_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;

    // 创建管理器窗口
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &manager_id,
        WebviewUrl::App("overlay-manager.html".into())
    )
    .title("Mecap Overlay Manager")
    .inner_size(1920.0, 1080.0)
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true);

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        _ => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 记录管理器信息
            let overlay_info = OverlayInfo {
                id: manager_id.clone(),
                window_type: OverlayType::ScreenshotOverlay,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(manager_id.clone(), overlay_info);
            }

            println!("[OVERLAY] Fullscreen overlay manager created: {}", manager_id);
            Ok(manager_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create overlay manager: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 关闭所有覆盖层
#[command]
pub async fn close_all_overlays(app_handle: AppHandle) -> Result<u32, String> {
    println!("[OVERLAY] Closing all overlays");

    let overlay_ids: Vec<String> = if let Ok(manager) = OVERLAY_MANAGER.lock() {
        manager.keys().cloned().collect()
    } else {
        return Err("Failed to access overlay manager".to_string());
    };

    let mut closed_count = 0;

    for overlay_id in overlay_ids {
        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            if window.close().is_ok() {
                closed_count += 1;
            }
        }
    }

    // 清空管理器
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.clear();
    }

    println!("[OVERLAY] Closed {} overlays", closed_count);
    Ok(closed_count)
}

/// 隐藏所有覆盖层（实时创建模式 - 关闭所有覆盖层）
#[command]
pub async fn hide_all_overlays(app_handle: AppHandle) -> Result<u32, String> {
    println!("[OVERLAY] 🔧 Closing all overlays (real-time creation mode)");

    let overlay_ids: Vec<String> = if let Ok(manager) = OVERLAY_MANAGER.lock() {
        manager.keys().cloned().collect()
    } else {
        return Err("Failed to access overlay manager".to_string());
    };

    let mut closed_count = 0;

    for overlay_id in overlay_ids {
        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            println!("[OVERLAY] 🔧 Closing overlay: {}", overlay_id);
            if window.close().is_ok() {
                closed_count += 1;
            }
        }
    }

    // 清除所有覆盖层的管理器记录
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.clear();
    }

    println!("[OVERLAY] 🔧 Closed {} overlays", closed_count);
    Ok(closed_count)
}

/// P1-T4: 高级区域选择功能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionSelection {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
    pub timestamp: u64,
    pub screen_width: u32,
    pub screen_height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OverlayUIConfig {
    pub show_coordinates: bool,
    pub show_grid: bool,
    pub show_magnifier: bool,
    pub grid_size: u32,
    pub selection_color: String,
    pub highlight_color: String,
}

impl Default for OverlayUIConfig {
    fn default() -> Self {
        Self {
            show_coordinates: true,
            show_grid: true,
            show_magnifier: true,
            grid_size: 20,
            selection_color: "#007AFF".to_string(),
            highlight_color: "#FF6B35".to_string(),
        }
    }
}

/// 创建高级区域选择覆盖层 - P1-T4核心功能
#[command]
pub async fn create_advanced_region_selector(app_handle: AppHandle, config: Option<OverlayUIConfig>) -> Result<String, String> {
    println!("[OVERLAY] Creating advanced region selector with enhanced UI");

    // 开始截图会话
    crate::modules::state::start_capture_session(app_handle.clone(), "region".to_string(), None).await
        .map_err(|e| format!("Failed to start capture session: {}", e))?;

    let ui_config = config.unwrap_or_default();
    let overlay_id = format!("advanced_region_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;

    // 创建高级区域选择覆盖层
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &overlay_id,
        WebviewUrl::App("overlay-advanced-region.html".into())
    )
    .title("Mecap Advanced Region Selector")
    .inner_size(1920.0, 1080.0)
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true);

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        _ => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 发送UI配置到前端
            window.emit("ui-config", &ui_config)
                .map_err(|e| format!("Failed to send UI config: {}", e))?;

            // 创建快捷操作栏
            let toolbar_config = QuickActionConfig::default();
            match create_quick_action_toolbar(app_handle.clone(), Some(toolbar_config), Some(overlay_id.clone())).await {
                Ok(toolbar_id) => {
                    println!("[OVERLAY] Quick action toolbar created for region selector: {}", toolbar_id);
                }
                Err(e) => {
                    println!("[WARNING] Failed to create quick action toolbar: {}", e);
                }
            }

            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: overlay_id.clone(),
                window_type: OverlayType::RegionSelector,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(overlay_id.clone(), overlay_info);
            }

            println!("[OVERLAY] Advanced region selector created: {}", overlay_id);
            Ok(overlay_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create advanced region selector: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 处理区域选择完成事件
#[command]
pub async fn handle_region_selection_complete(
    app_handle: AppHandle,
    selection: RegionSelection,
    action: String
) -> Result<String, String> {
    println!("[OVERLAY] Region selection completed: {:?}", selection);

    // 更新状态管理
    let region_data = crate::modules::state::RegionData {
        x: selection.x,
        y: selection.y,
        width: selection.width,
        height: selection.height,
        screen_width: selection.screen_width,
        screen_height: selection.screen_height,
    };

    crate::modules::state::complete_capture_session(app_handle.clone(), Some(region_data), None).await
        .map_err(|e| format!("Failed to complete capture session: {}", e))?;

    match action.as_str() {
        "capture" => {
            // 执行截图捕获
            let area = crate::modules::capture::ScreenshotArea {
                x: selection.x,
                y: selection.y,
                width: selection.width,
                height: selection.height,
            };

            match crate::modules::capture::capture_region_new(area).await {
                Ok(result) => {
                    println!("[OVERLAY] Region captured successfully");

                    // 关闭覆盖层
                    close_all_overlays(app_handle).await?;

                    Ok(format!("Region captured: {}x{}", result.width.unwrap_or(0), result.height.unwrap_or(0)))
                }
                Err(e) => Err(format!("Failed to capture region: {}", e))
            }
        }
        "copy" => {
            // TODO: 实现复制到剪贴板功能
            println!("[OVERLAY] Copy to clipboard requested");
            Ok("Copy functionality not yet implemented".to_string())
        }
        "save" => {
            // 保存选择区域信息
            println!("[OVERLAY] Save selection requested");
            Ok("Selection saved".to_string())
        }
        _ => {
            Err(format!("Unknown action: {}", action))
        }
    }
}

/// 创建窗口高亮覆盖层 - P1-T4增强功能
#[command]
pub async fn create_window_highlight_overlay(app_handle: AppHandle, config: Option<OverlayUIConfig>) -> Result<String, String> {
    println!("[OVERLAY] Creating window highlight overlay with enhanced UI");

    let ui_config = config.unwrap_or_default();
    let overlay_id = format!("window_highlight_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;
    println!("[OVERLAY] 🎯 Detected overlay mode: {:?}", overlay_mode);

    // 创建窗口高亮覆盖层
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &overlay_id,
        WebviewUrl::App("overlay-window-highlight.html".into())
    )
    .title("Mecap Window Highlight")
    .inner_size(3840.0, 2160.0)  // 使用更大的初始尺寸，后面会根据实际显示器调整
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true)
    .fullscreen(false)  // 明确禁用系统全屏模式
    .accept_first_mouse(true)  // 确保能接收鼠标事件
    .content_protected(false); // 确保内容不被保护

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent | OverlayMode::SemiTransparent => {
            println!("[OVERLAY] 🎯 Setting window transparent: true (mode: {:?})", overlay_mode);
            window_builder = window_builder.transparent(true);
        }
        _ => {
            println!("[OVERLAY] 🎯 Setting window transparent: false (mode: {:?})", overlay_mode);
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            println!("[OVERLAY] 🎯 Initializing Tauri API in overlay window");

            // 🔧 CRITICAL FIX: Ensure cursor events are NOT ignored
            println!("[OVERLAY] 🔧 Ensuring cursor events are enabled for click handling");
            if let Err(e) = window.set_ignore_cursor_events(false) {
                println!("[WARNING] Failed to enable cursor events: {}", e);
            } else {
                println!("[OVERLAY] ✅ Cursor events enabled successfully");
            }

            // 🔧 CRITICAL DEBUG: 启用开发者工具来查看前端日志
            #[cfg(debug_assertions)]
            {
                println!("[OVERLAY] 🔧 Opening DevTools for frontend debugging");
                match window.open_devtools() {
                    Err(e) => println!("[WARNING] Failed to open DevTools: {}", e),
                    Ok(_) => println!("[OVERLAY] ✅ DevTools opened successfully"),
                }
            }

            // 确保Tauri API在覆盖层中可用
            if let Err(e) = window.eval("
                console.log('[OVERLAY] 🔍 Checking Tauri API availability...');
                if (typeof window.__TAURI__ === 'undefined') {
                    console.error('[OVERLAY] ❌ Tauri API not available');
                    window.tauriApiAvailable = false;
                } else {
                    console.log('[OVERLAY] ✅ Tauri API is available');
                    console.log('[OVERLAY] 🔍 Available Tauri modules:', Object.keys(window.__TAURI__));
                    window.tauriApiAvailable = true;
                }
            ") {
                println!("[WARNING] Failed to check Tauri API: {}", e);
            }

            // 🚫 不使用系统全屏模式，因为会拦截ESC键
            // 而是手动设置窗口大小和位置来覆盖整个屏幕
            println!("[OVERLAY] 🎯 Setting manual fullscreen to avoid ESC key interception");

            // 获取主显示器尺寸
            if let Ok(Some(monitor)) = window.primary_monitor() {
                let size = monitor.size();
                let position = monitor.position();

                println!("[OVERLAY] 🎯 Monitor size: {}x{}, position: ({}, {})",
                    size.width, size.height, position.x, position.y);

                // 设置窗口大小和位置（覆盖整个屏幕包括菜单栏）
                if let Err(e) = window.set_size(tauri::Size::Physical(*size)) {
                    println!("[WARNING] Failed to set window size: {}", e);
                }
                if let Err(e) = window.set_position(tauri::Position::Physical(*position)) {
                    println!("[WARNING] Failed to set window position: {}", e);
                }

                println!("[OVERLAY] 🎯 Window positioned to cover entire screen including menu bar");

                println!("[OVERLAY] 🎯 Manual fullscreen setup completed");
            } else {
                println!("[WARNING] Failed to get monitor info, falling back to default size");
            }

            // 强制设置焦点以确保键盘事件能被捕获
            println!("[OVERLAY] 🎯 Setting window focus for keyboard events");
            if let Err(e) = window.set_focus() {
                println!("[WARNING] Failed to set window focus: {}", e);
            } else {
                println!("[OVERLAY] 🎯 Window focus set successfully");
            }

            // 额外的焦点确保措施
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            if let Err(e) = window.set_focus() {
                println!("[WARNING] Second focus attempt failed: {}", e);
            }

            // 设置窗口为可接收键盘输入
            println!("[OVERLAY] 🎯 Configuring window for keyboard input");
            if let Err(e) = window.eval("window.focus(); document.body.focus();") {
                println!("[WARNING] Failed to focus window via JavaScript: {}", e);
            }

            // 等待窗口加载完成
            println!("[OVERLAY] 🔍 Waiting for window to load...");
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

            // 检查窗口是否可见
            println!("[OVERLAY] 🔍 Window is visible: {:?}", window.is_visible());

            // 应用模式特定的样式
            println!("[OVERLAY] Applying overlay mode styles: {:?}", overlay_mode);
            apply_overlay_mode_styles(&window, &overlay_mode).await?;
            println!("[OVERLAY] Overlay mode styles applied successfully");

            // 🔧 TEMPORARILY DISABLED: 综合键盘事件诊断和修复
            println!("[OVERLAY] 🔍 SKIPPING diagnostic JavaScript to allow HTML JavaScript to run...");

            // 简单的测试JavaScript，不干扰HTML的JavaScript
            let diagnostic_js = r#"
                console.log('[DIAGNOSTIC] Simple test - not interfering with HTML JavaScript');
                // 返回简单状态
                JSON.stringify({ status: 'diagnostic_skipped', html_js_allowed: true });
            "#;

            /*
            println!("[OVERLAY] 🔍 Starting COMPREHENSIVE keyboard event diagnostics...");
            let diagnostic_js = r#"
                // === COMPREHENSIVE DIAGNOSTIC SYSTEM ===
                console.log('[FRONTEND] === STARTING COMPREHENSIVE DIAGNOSTICS ===');

                // === 创建全局诊断对象 ===
                window.MECAP_DIAGNOSTICS = {
                    escPressCount: 0,
                    lastEscTime: 0,
                    domSignalsSent: 0,
                    eventListenersAttached: 0,
                    tauriApiChecks: [],
                    windowStateChecks: [],
                    communicationLog: []
                };

                // === 日志函数 ===
                function logDiagnostic(category, message, data = null) {
                    const timestamp = Date.now();
                    const logEntry = { timestamp, category, message, data };
                    window.MECAP_DIAGNOSTICS.communicationLog.push(logEntry);
                    console.log(`[FRONTEND-${category}] ${message}`, data || '');

                    // 同时更新屏幕显示
                    const diagnosticDiv = document.getElementById('keyboard-diagnostic');
                    if (diagnosticDiv) {
                        const logLine = `<br><span style="color: cyan;">[${category}] ${message}</span>`;
                        diagnosticDiv.innerHTML += logLine;
                    }
                }

                logDiagnostic('INIT', 'Comprehensive diagnostic system initialized');
                // === TAURI API 全面检查 ===
                logDiagnostic('TAURI', 'Starting Tauri API availability check');
                const tauriChecks = {
                    'window.__TAURI__': typeof window.__TAURI__,
                    'window.Tauri': typeof window.Tauri,
                    'window.__TAURI_INTERNALS__': typeof window.__TAURI_INTERNALS__,
                    'window.__TAURI_METADATA__': typeof window.__TAURI_METADATA__,
                    'window.__TAURI__.core': window.__TAURI__ ? typeof window.__TAURI__.core : 'N/A',
                    'window.__TAURI__.core.invoke': window.__TAURI__ && window.__TAURI__.core ? typeof window.__TAURI__.core.invoke : 'N/A'
                };
                window.MECAP_DIAGNOSTICS.tauriApiChecks = tauriChecks;
                logDiagnostic('TAURI', 'API check results', tauriChecks);

                // === 基础状态检查 ===
                const bodyContent = document.body ? document.body.innerHTML.length : 0;
                const title = document.title || 'No title';

                // === 焦点和可见性检查 ===
                const hasFocus = document.hasFocus();
                const isVisible = document.visibilityState === 'visible';
                const activeElement = document.activeElement ? document.activeElement.tagName : 'NONE';

                // === 窗口属性检查 ===
                const windowFocused = window.document.hasFocus();
                const tabIndex = document.body.tabIndex;

                const windowState = { hasFocus, isVisible, activeElement, windowFocused, tabIndex };
                window.MECAP_DIAGNOSTICS.windowStateChecks = windowState;
                logDiagnostic('WINDOW', 'Window state check', windowState);

                // === 创建诊断显示 ===
                if (document.body) {
                    const diagnosticDiv = document.createElement('div');
                    diagnosticDiv.id = 'keyboard-diagnostic';
                    diagnosticDiv.style.cssText = `
                        position: fixed; top: 50px; left: 10px;
                        color: lime; font-size: 16px; z-index: 10000;
                        background: rgba(0,0,0,0.8); padding: 10px;
                        font-family: monospace; line-height: 1.4;
                    `;
                    diagnosticDiv.innerHTML = `
                        KEYBOARD DIAGNOSTICS:<br>
                        Focus: ${hasFocus}<br>
                        Visible: ${isVisible}<br>
                        Active: ${activeElement}<br>
                        TabIndex: ${tabIndex}<br>
                        Events: <span id="event-count">0</span>
                    `;
                    document.body.appendChild(diagnosticDiv);
                }

                // === 确保body可以接收焦点 ===
                if (document.body) {
                    document.body.tabIndex = 0;
                    document.body.focus();
                    document.body.style.outline = 'none';
                }

                // === 多层键盘事件监听器 ===
                let eventCount = 0;

                // === 综合ESC键处理器 ===
                logDiagnostic('LISTENER', 'Setting up comprehensive ESC key handler');

                function handleEscKeyPress(e, source) {
                    eventCount++;
                    const counter = document.getElementById('event-count');
                    if (counter) counter.textContent = eventCount;

                    logDiagnostic('KEY', `Key pressed: ${e.key} from ${source}`, {
                        key: e.key,
                        code: e.code,
                        target: e.target.tagName,
                        timestamp: Date.now()
                    });

                    if (e.key === 'Escape') {
                        window.MECAP_DIAGNOSTICS.escPressCount++;
                        window.MECAP_DIAGNOSTICS.lastEscTime = Date.now();

                        logDiagnostic('ESC', `ESC key detected! Count: ${window.MECAP_DIAGNOSTICS.escPressCount}`);

                        // === STEP 1: 尝试Tauri API ===
                        let tauriApi = null;
                        let tauriMethod = 'none';

                        if (window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.core.invoke) {
                            tauriApi = window.__TAURI__;
                            tauriMethod = 'standard';
                        } else if (window.Tauri && window.Tauri.core && window.Tauri.core.invoke) {
                            tauriApi = window.Tauri;
                            tauriMethod = 'global';
                        } else if (window.__TAURI_INTERNALS__ && window.__TAURI_INTERNALS__.core) {
                            tauriApi = window.__TAURI_INTERNALS__;
                            tauriMethod = 'internals';
                        }

                        logDiagnostic('TAURI', `Tauri API method: ${tauriMethod}`, { available: !!tauriApi });

                        if (tauriApi) {
                            logDiagnostic('TAURI', 'Attempting Tauri API call');
                            tauriApi.core.invoke('exit_capture_completely_direct')
                                .then(() => {
                                    logDiagnostic('TAURI', 'Tauri API call successful');
                                })
                                .catch(err => {
                                    logDiagnostic('TAURI', 'Tauri API call failed', err);
                                    // 如果Tauri失败，继续到DOM方法
                                    sendDomSignal();
                                });
                        } else {
                            // === STEP 2: DOM信号方法 ===
                            sendDomSignal();
                        }

                        function sendDomSignal() {
                            logDiagnostic('DOM', 'Starting DOM signal method');
                            window.MECAP_DIAGNOSTICS.domSignalsSent++;

                            // 方法1: 创建DOM标记
                            const escMarker = document.createElement('div');
                            escMarker.id = 'esc-key-pressed-marker-' + Date.now();
                            escMarker.className = 'esc-marker';
                            escMarker.style.display = 'none';
                            escMarker.setAttribute('data-timestamp', Date.now().toString());
                            escMarker.setAttribute('data-count', window.MECAP_DIAGNOSTICS.escPressCount.toString());
                            document.body.appendChild(escMarker);

                            // 方法2: 修改页面标题
                            const newTitle = 'ESC_PRESSED_' + Date.now() + '_COUNT_' + window.MECAP_DIAGNOSTICS.escPressCount;
                            document.title = newTitle;

                            // 方法3: 创建全局变量
                            window.ESC_KEY_PRESSED = {
                                timestamp: Date.now(),
                                count: window.MECAP_DIAGNOSTICS.escPressCount,
                                method: 'dom_signal'
                            };

                            // 方法4: 修改body属性
                            document.body.setAttribute('data-esc-pressed', Date.now().toString());
                            document.body.setAttribute('data-esc-count', window.MECAP_DIAGNOSTICS.escPressCount.toString());

                            logDiagnostic('DOM', 'DOM signals sent', {
                                markerId: escMarker.id,
                                title: newTitle,
                                bodyAttribute: document.body.getAttribute('data-esc-pressed'),
                                globalVar: !!window.ESC_KEY_PRESSED
                            });
                        }
                    }
                }

                // 监听器1: document级别 (capture phase)
                document.addEventListener('keydown', function(e) {
                    handleEscKeyPress(e, 'document-capture');
                }, true);

                // 监听器2: document级别 (bubble phase)
                document.addEventListener('keydown', function(e) {
                    handleEscKeyPress(e, 'document-bubble');
                }, false);

                window.MECAP_DIAGNOSTICS.eventListenersAttached += 2;
                logDiagnostic('LISTENER', 'Document event listeners attached');

                // 监听器2: window级别
                window.addEventListener('keydown', function(e) {
                    console.log(`[DIAG-WIN] Key: ${e.key}, Code: ${e.code}`);
                }, true);

                // 监听器3: body级别
                if (document.body) {
                    document.body.addEventListener('keydown', function(e) {
                        console.log(`[DIAG-BODY] Key: ${e.key}, Code: ${e.code}`);
                    });
                }

                // === 测试键盘事件触发 ===
                setTimeout(() => {
                    console.log('[DIAG] Simulating test keydown event...');
                    const testEvent = new KeyboardEvent('keydown', {
                        key: 'F12',
                        code: 'F12',
                        bubbles: true,
                        cancelable: true
                    });
                    document.dispatchEvent(testEvent);
                }, 1000);

                // === 检查原始函数 ===
                const hasHandleKeyboard = typeof handleKeyboard === 'function';
                const hasHandleEscapeKey = typeof handleEscapeKey === 'function';

                // === 返回诊断信息 ===
                JSON.stringify({
                    html: bodyContent > 0,
                    title: title,
                    size: bodyContent,
                    tauri: typeof window.__TAURI__ !== 'undefined',
                    focus: hasFocus,
                    visible: isVisible,
                    active: activeElement,
                    tabIndex: tabIndex,
                    keyboardFn: hasHandleKeyboard,
                    escFn: hasHandleEscapeKey
                });
            */

            match window.eval(diagnostic_js) {
                Ok(result) => {
                    println!("[OVERLAY] 🔍 Diagnostic JavaScript executed successfully");
                    println!("[OVERLAY] 🔍 Diagnostic result: {:?}", result);

                    // 尝试解析诊断结果（如果有返回值）
                    println!("[OVERLAY] 🔍 Result type: {:?}", std::any::type_name_of_val(&result));

                    // 由于eval可能返回不同类型，我们先检查结果
                    match format!("{:?}", result).as_str() {
                        "()" => println!("[OVERLAY] 🔍 JavaScript returned void - this is expected for diagnostic code"),
                        other => {
                            println!("[OVERLAY] 🔍 JavaScript returned: {}", other);
                            // 尝试解析JSON如果看起来像JSON
                            if other.starts_with('"') && other.ends_with('"') {
                                let json_str = &other[1..other.len()-1]; // 移除引号
                                if let Ok(diagnostic_json) = serde_json::from_str::<serde_json::Value>(json_str) {
                                    println!("[OVERLAY] 🔍 Parsed diagnostics:");
                                    println!("[OVERLAY] 🔍   - HTML loaded: {}", diagnostic_json.get("html").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Has focus: {}", diagnostic_json.get("focus").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Is visible: {}", diagnostic_json.get("visible").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Active element: {}", diagnostic_json.get("active").unwrap_or(&serde_json::Value::String("UNKNOWN".to_string())));
                                    println!("[OVERLAY] 🔍   - Tab index: {}", diagnostic_json.get("tabIndex").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(-1))));
                                    println!("[OVERLAY] 🔍   - Tauri available: {}", diagnostic_json.get("tauri").unwrap_or(&serde_json::Value::Bool(false)));
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    println!("[WARNING] Failed to execute diagnostic JavaScript: {}", e);
                }
            }

            // 发送UI配置到前端
            window.emit("ui-config", &ui_config)
                .map_err(|e| format!("Failed to send UI config: {}", e))?;

            // 启动智能窗口检测
            crate::modules::window::start_smart_window_detection(app_handle.clone()).await?;

            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: overlay_id.clone(),
                window_type: OverlayType::WindowHighlight,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(overlay_id.clone(), overlay_info);
            }

            // 等待窗口完全加载
            println!("[OVERLAY] 🎯 Waiting for window to load...");
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

            // 注册全局ESC快捷键
            println!("[OVERLAY] 🎯 Registering global ESC shortcut for capture exit");
            if let Err(e) = crate::modules::global_shortcuts::register_global_esc_shortcut(&app_handle) {
                println!("[OVERLAY] ⚠️ Failed to register global ESC shortcut: {}", e);
                println!("[OVERLAY] 🎯 Falling back to backend ESC key monitoring");

                // 如果全局快捷键注册失败，启动后端监听作为备用方案
                let app_handle_clone = app_handle.clone();
                let overlay_id_clone = overlay_id.clone();

                tokio::spawn(async move {
                println!("[BACKEND-MONITOR] === STARTING COMPREHENSIVE BACKEND ESC MONITORING ===");

                // 每100ms检查一次是否需要退出
                let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(100));
                let mut check_count = 0;
                let mut last_diagnostic_dump = 0;

                loop {
                    interval.tick().await;
                    check_count += 1;

                    // 检查overlay窗口是否还存在
                    if let Some(overlay_window) = app_handle_clone.get_webview_window(&overlay_id_clone) {
                        // 每5秒输出详细状态
                        if check_count % 50 == 0 {
                            let seconds = check_count / 10;
                            println!("[BACKEND-MONITOR] 🎯 Active monitoring ({}s) - Check #{}", seconds, check_count);

                            // 每10秒进行一次全面诊断
                            if seconds % 10 == 0 && seconds != last_diagnostic_dump {
                                last_diagnostic_dump = seconds;
                                println!("[BACKEND-MONITOR] 🔍 Performing comprehensive diagnostic check...");

                                // 获取前端诊断状态
                                let diagnostic_js = r#"
                                    JSON.stringify({
                                        diagnostics: window.MECAP_DIAGNOSTICS || {},
                                        escPressed: window.ESC_KEY_PRESSED || null,
                                        title: document.title,
                                        bodyEscAttr: document.body.getAttribute('data-esc-pressed'),
                                        bodyEscCount: document.body.getAttribute('data-esc-count'),
                                        escMarkers: Array.from(document.querySelectorAll('.esc-marker')).length,
                                        allMarkers: Array.from(document.querySelectorAll('[id*="esc-key-pressed"]')).map(el => el.id)
                                    });
                                "#;

                                match overlay_window.eval(diagnostic_js) {
                                    Ok(result) => {
                                        let result_str = format!("{:?}", result);
                                        println!("[BACKEND-MONITOR] 🔍 Frontend diagnostic result: {}", result_str);

                                        // 尝试解析JSON
                                        if let Some(json_start) = result_str.find('{') {
                                            if let Some(json_end) = result_str.rfind('}') {
                                                let json_str = &result_str[json_start..=json_end];
                                                if let Ok(diagnostic_data) = serde_json::from_str::<serde_json::Value>(json_str) {
                                                    println!("[BACKEND-MONITOR] 🔍 Parsed diagnostics:");
                                                    if let Some(diagnostics) = diagnostic_data.get("diagnostics") {
                                                        println!("[BACKEND-MONITOR] 🔍   ESC press count: {}", diagnostics.get("escPressCount").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                        println!("[BACKEND-MONITOR] 🔍   DOM signals sent: {}", diagnostics.get("domSignalsSent").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                        println!("[BACKEND-MONITOR] 🔍   Last ESC time: {}", diagnostics.get("lastEscTime").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                    }
                                                    println!("[BACKEND-MONITOR] 🔍   Title: {}", diagnostic_data.get("title").unwrap_or(&serde_json::Value::String("N/A".to_string())));
                                                    println!("[BACKEND-MONITOR] 🔍   Body ESC attr: {}", diagnostic_data.get("bodyEscAttr").unwrap_or(&serde_json::Value::Null));
                                                    println!("[BACKEND-MONITOR] 🔍   ESC markers: {}", diagnostic_data.get("escMarkers").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                    println!("[BACKEND-MONITOR] 🔍   All markers: {:?}", diagnostic_data.get("allMarkers").unwrap_or(&serde_json::Value::Array(vec![])));
                                                }
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        println!("[BACKEND-MONITOR] ❌ Failed to get diagnostic data: {}", e);
                                    }
                                }
                            }
                        }

                        // 每次都检查ESC键标记（多种方法）
                        let comprehensive_check_js = r#"
                            {
                                // 方法1: 检查特定ID的标记
                                oldMarker: document.getElementById('esc-key-pressed-marker') !== null,

                                // 方法2: 检查所有ESC标记
                                anyEscMarker: document.querySelectorAll('[id*="esc-key-pressed"]').length > 0,

                                // 方法3: 检查CSS类标记
                                classMarkers: document.querySelectorAll('.esc-marker').length,

                                // 方法4: 检查页面标题
                                titleCheck: document.title.includes('ESC_PRESSED_'),

                                // 方法5: 检查全局变量
                                globalVar: typeof window.ESC_KEY_PRESSED !== 'undefined',

                                // 方法6: 检查body属性
                                bodyAttr: document.body.hasAttribute('data-esc-pressed'),

                                // 详细信息
                                details: {
                                    title: document.title,
                                    bodyEscAttr: document.body.getAttribute('data-esc-pressed'),
                                    bodyEscCount: document.body.getAttribute('data-esc-count'),
                                    globalVarValue: window.ESC_KEY_PRESSED || null
                                }
                            }
                        "#;

                        match overlay_window.eval(comprehensive_check_js) {
                            Ok(result) => {
                                let result_str = format!("{:?}", result);

                                // 检查是否有任何ESC信号
                                let has_esc_signal = result_str.contains("true") ||
                                                   result_str.contains("ESC_PRESSED_") ||
                                                   result_str.contains("\"classMarkers\":") && !result_str.contains("\"classMarkers\":0");

                                if has_esc_signal {
                                    println!("[BACKEND-MONITOR] 🎯 ESC SIGNAL DETECTED! Result: {}", result_str);
                                    println!("[BACKEND-MONITOR] 🎯 Triggering exit sequence...");

                                    // 调用退出函数
                                    match crate::modules::ux::exit_capture_completely_direct(app_handle_clone.clone()).await {
                                        Ok(_) => {
                                            println!("[BACKEND-MONITOR] ✅ Successfully exited capture via backend monitor");
                                        }
                                        Err(e) => {
                                            println!("[BACKEND-MONITOR] ❌ Failed to exit capture: {}", e);
                                        }
                                    }
                                    break;
                                } else if check_count % 100 == 0 {
                                    // 每10秒输出一次检查结果（用于调试）
                                    println!("[BACKEND-MONITOR] 🔍 No ESC signal detected. Check result: {}", result_str);
                                }
                            }
                            Err(e) => {
                                println!("[BACKEND-MONITOR] ❌ Failed to evaluate ESC check: {}", e);
                            }
                        }

                        // 检查窗口是否仍然可见
                        if let Ok(is_visible) = overlay_window.is_visible() {
                            if !is_visible {
                                println!("[BACKEND-MONITOR] 🎯 Overlay window became invisible - stopping monitor");
                                break;
                            }
                        }
                    } else {
                        println!("[BACKEND-MONITOR] 🎯 Overlay window no longer exists - stopping monitor");
                        break;
                    }

                    // 超时保护：60秒后自动停止监听（增加到60秒用于调试）
                    if check_count > 600 {
                        println!("[BACKEND-MONITOR] 🎯 Backend ESC monitor timeout (60s) - stopping");
                        break;
                    }
                }

                    println!("[BACKEND-MONITOR] === ESC MONITORING STOPPED ===");
                });
            } else {
                println!("[OVERLAY] ✅ Global ESC shortcut registered successfully - no fallback needed");
            }

            println!("[OVERLAY] Window highlight overlay created: {}", overlay_id);
            Ok(overlay_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create window highlight overlay: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 处理窗口选择完成事件
#[command]
pub async fn handle_window_selection_complete(
    app_handle: AppHandle,
    window_id: u32,
    action: String
) -> Result<String, String> {
    println!("[OVERLAY] Window selection completed: window_id={}, action={}", window_id, action);

    match action.as_str() {
        "capture" => {
            // 获取窗口信息
            if let Ok(Some(window_info)) = crate::modules::window::get_window_info_new(window_id as u64).await {
                // 执行窗口截图
                let area = crate::modules::capture::ScreenshotArea {
                    x: window_info.x as f64,
                    y: window_info.y as f64,
                    width: window_info.width as f64,
                    height: window_info.height as f64,
                };

                match crate::modules::capture::capture_region_new(area).await {
                    Ok(result) => {
                        println!("[OVERLAY] Window captured successfully");

                        // 关闭覆盖层
                        close_all_overlays(app_handle).await?;

                        Ok(format!("Window captured: {}x{}", result.width.unwrap_or(0), result.height.unwrap_or(0)))
                    }
                    Err(e) => Err(format!("Failed to capture window: {}", e))
                }
            } else {
                Err("Window not found".to_string())
            }
        }
        "highlight" => {
            // 持续高亮窗口
            println!("[OVERLAY] Window highlight mode activated");
            Ok("Window highlighted".to_string())
        }
        _ => {
            Err(format!("Unknown action: {}", action))
        }
    }
}

/// 获取覆盖层UI配置
#[command]
pub async fn get_overlay_ui_config() -> Result<OverlayUIConfig, String> {
    Ok(OverlayUIConfig::default())
}

/// 更新覆盖层UI配置
#[command]
pub async fn update_overlay_ui_config(config: OverlayUIConfig) -> Result<(), String> {
    println!("[OVERLAY] Updating UI config: {:?}", config);
    // TODO: 持久化配置到本地存储
    Ok(())
}

/// P1-T5: 快捷操作栏功能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuickActionConfig {
    pub show_capture_tools: bool,
    pub show_edit_tools: bool,
    pub show_save_options: bool,
    pub toolbar_position: String, // "top", "bottom", "follow_cursor"
    pub auto_hide_delay: u32, // seconds
}

impl Default for QuickActionConfig {
    fn default() -> Self {
        Self {
            show_capture_tools: true,
            show_edit_tools: true,
            show_save_options: true,
            toolbar_position: "bottom".to_string(),
            auto_hide_delay: 5,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolbarAction {
    pub action_type: String,
    pub data: serde_json::Value,
    pub timestamp: u64,
}

/// 创建快捷操作栏覆盖层
#[command]
pub async fn create_quick_action_toolbar(
    app_handle: AppHandle,
    config: Option<QuickActionConfig>,
    parent_overlay_id: Option<String>
) -> Result<String, String> {
    println!("[OVERLAY] Creating quick action toolbar");

    let toolbar_config = config.unwrap_or_default();
    let toolbar_id = format!("quick_toolbar_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;

    // 创建快捷操作栏覆盖层
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &toolbar_id,
        WebviewUrl::App("overlay-quick-toolbar.html".into())
    )
    .title("Mecap Quick Toolbar")
    .inner_size(400.0, 80.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(false); // 不抢夺焦点

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        _ => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置工具栏位置
            position_toolbar(&window, &toolbar_config.toolbar_position).await?;

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 发送配置到前端
            window.emit("toolbar-config", &toolbar_config)
                .map_err(|e| format!("Failed to send toolbar config: {}", e))?;

            // 如果有父覆盖层，发送关联信息
            if let Some(parent_id) = parent_overlay_id {
                window.emit("parent-overlay", &parent_id)
                    .map_err(|e| format!("Failed to send parent overlay info: {}", e))?;
            }

            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: toolbar_id.clone(),
                window_type: OverlayType::QuickToolbar,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(toolbar_id.clone(), overlay_info);
            }

            println!("[OVERLAY] Quick action toolbar created: {}", toolbar_id);
            Ok(toolbar_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create quick action toolbar: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 处理快捷操作栏动作
#[command]
pub async fn handle_toolbar_action(
    app_handle: AppHandle,
    action: ToolbarAction
) -> Result<String, String> {
    println!("[OVERLAY] Handling toolbar action: {:?}", action.action_type);

    match action.action_type.as_str() {
        "capture_region" => {
            // 触发区域选择
            create_advanced_region_selector(app_handle, None).await
        }
        "capture_window" => {
            // 触发窗口选择
            create_window_highlight_overlay(app_handle, None).await
        }
        "save_screenshot" => {
            // 保存当前截图
            println!("[TOOLBAR] Save screenshot action");
            Ok("Screenshot saved".to_string())
        }
        "copy_to_clipboard" => {
            // 复制到剪贴板
            println!("[TOOLBAR] Copy to clipboard action");
            Ok("Copied to clipboard".to_string())
        }
        "edit_screenshot" => {
            // 打开编辑器
            println!("[TOOLBAR] Edit screenshot action");
            Ok("Editor opened".to_string())
        }
        "close_toolbar" => {
            // 关闭工具栏
            match close_all_overlays(app_handle).await {
                Ok(count) => Ok(format!("Closed {} overlays", count)),
                Err(e) => Err(e)
            }
        }
        _ => {
            Err(format!("Unknown toolbar action: {}", action.action_type))
        }
    }
}

/// 设置工具栏位置
async fn position_toolbar(window: &tauri::WebviewWindow, position: &str) -> Result<(), String> {
    match position {
        "top" => {
            window.set_position(tauri::Position::Logical(tauri::LogicalPosition { x: 0.0, y: 0.0 }))
                .map_err(|e| format!("Failed to position toolbar at top: {}", e))?;
        }
        "bottom" => {
            // 获取屏幕尺寸并设置到底部
            window.set_position(tauri::Position::Logical(tauri::LogicalPosition { x: 0.0, y: 900.0 }))
                .map_err(|e| format!("Failed to position toolbar at bottom: {}", e))?;
        }
        "center" => {
            window.center()
                .map_err(|e| format!("Failed to center toolbar: {}", e))?;
        }
        _ => {
            // 默认居中
            window.center()
                .map_err(|e| format!("Failed to center toolbar: {}", e))?;
        }
    }
    Ok(())
}

/// 更新工具栏位置（跟随鼠标或其他元素）
#[command]
pub async fn update_toolbar_position(
    app_handle: AppHandle,
    toolbar_id: String,
    x: f64,
    y: f64
) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window(&toolbar_id) {
        window.set_position(tauri::Position::Logical(tauri::LogicalPosition { x, y }))
            .map_err(|e| format!("Failed to update toolbar position: {}", e))?;
        println!("[TOOLBAR] Position updated: ({}, {})", x, y);
    }
    Ok(())
}

/// 获取快捷操作栏配置
#[command]
pub async fn get_quick_action_config() -> Result<QuickActionConfig, String> {
    Ok(QuickActionConfig::default())
}

/// 更新快捷操作栏配置
#[command]
pub async fn update_quick_action_config(config: QuickActionConfig) -> Result<(), String> {
    println!("[OVERLAY] Updating quick action config: {:?}", config);
    // TODO: 持久化配置到本地存储
    Ok(())
}

/// 获取覆盖层兼容性信息
#[command]
pub async fn get_overlay_compatibility_info() -> Result<String, String> {
    let mut info = Vec::new();
    info.push("=== Overlay Compatibility Information ===".to_string());

    // 平台信息
    #[cfg(target_os = "macos")]
    {
        info.push("Platform: macOS".to_string());
        info.push("✅ Full transparency support".to_string());
        info.push("✅ Always on top support".to_string());
        info.push("✅ Fullscreen overlay support".to_string());
    }

    #[cfg(target_os = "windows")]
    {
        info.push("Platform: Windows".to_string());
        info.push("✅ Full transparency support".to_string());
        info.push("✅ Always on top support".to_string());
        info.push("✅ Fullscreen overlay support".to_string());
    }

    #[cfg(target_os = "linux")]
    {
        info.push("Platform: Linux".to_string());

        // 检测合成器
        use std::process::Command;
        let mut compositor_found = false;
        let compositors = ["mutter", "kwin_x11", "kwin_wayland", "compiz", "xfwm4"];

        for compositor in &compositors {
            if let Ok(output) = Command::new("pgrep").arg(compositor).output() {
                if output.status.success() && !output.stdout.is_empty() {
                    info.push(format!("✅ Compositor detected: {}", compositor));
                    info.push("✅ Transparency support available".to_string());
                    compositor_found = true;
                    break;
                }
            }
        }

        if !compositor_found {
            info.push("⚠️ No compositor detected".to_string());
            info.push("⚠️ Limited transparency support".to_string());
        }
    }

    // 检测当前模式
    let current_mode = detect_best_overlay_mode().await;
    info.push(format!("\nRecommended mode: {:?}", current_mode));

    // 功能支持
    info.push("\n=== Feature Support ===".to_string());
    info.push("✅ Fullscreen overlays".to_string());
    info.push("✅ Multi-monitor support".to_string());
    info.push("✅ DPI scaling support".to_string());
    info.push("✅ Cross-platform compatibility".to_string());

    let report = info.join("\n");
    println!("[OVERLAY] Compatibility info: {}", report);
    Ok(report)
}

// === 混合截图功能扩展 ===

/// 创建多显示器截图覆盖层
#[command]
pub async fn create_multi_monitor_screenshot_overlay(app_handle: AppHandle) -> Result<Vec<String>, String> {
    use xcap::Monitor;

    println!("[OVERLAY] Creating multi-monitor screenshot overlay");

    // 获取所有显示器
    let monitors = Monitor::all()
        .map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors detected".to_string());
    }

    let mut overlay_ids = Vec::new();
    let timestamp = chrono::Utc::now().timestamp_millis();

    // 为每个显示器创建覆盖层
    for (index, monitor) in monitors.iter().enumerate() {
        let overlay_id = format!("screenshot_overlay_{}_{}", index, timestamp);

        // 检测最佳覆盖层模式
        let overlay_mode = detect_best_overlay_mode().await;

        // 创建覆盖层窗口
        let mut window_builder = WebviewWindowBuilder::new(
            &app_handle,
            &overlay_id,
            WebviewUrl::App("overlay-screenshot.html".into())
        )
        .title(&format!("Mecap Screenshot Overlay - Monitor {}", index))
        .inner_size(monitor.width().unwrap_or(1920) as f64, monitor.height().unwrap_or(1080) as f64)
        .position(monitor.x().unwrap_or(0) as f64, monitor.y().unwrap_or(0) as f64)
        .decorations(false)
        .always_on_top(true)
        .resizable(false)
        .skip_taskbar(true)
        .focused(index == 0); // 只有主显示器的覆盖层获得焦点

        // 根据模式设置透明度
        match overlay_mode {
            OverlayMode::FullTransparent => {
                window_builder = window_builder.transparent(true);
            }
            OverlayMode::SemiTransparent | OverlayMode::Opaque => {
                window_builder = window_builder.transparent(false);
            }
        }

        match window_builder.build() {
            Ok(window) => {
                // 应用模式特定的样式
                apply_overlay_mode_styles(&window, &overlay_mode).await?;

                // 发送显示器信息到前端
                let monitor_info = serde_json::json!({
                    "index": index,
                    "x": monitor.x().unwrap_or(0),
                    "y": monitor.y().unwrap_or(0),
                    "width": monitor.width().unwrap_or(1920),
                    "height": monitor.height().unwrap_or(1080),
                    "scale_factor": monitor.scale_factor().unwrap_or(1.0),
                    "is_primary": monitor.is_primary().unwrap_or(false)
                });

                if let Err(e) = window.emit("monitor-info", &monitor_info) {
                    println!("[WARNING] Failed to emit monitor info: {}", e);
                }

                // 记录覆盖层信息
                let overlay_info = OverlayInfo {
                    id: overlay_id.clone(),
                    window_type: OverlayType::ScreenshotOverlay,
                    created_at: timestamp as u64,
                    mode: overlay_mode,
                };

                if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                    manager.insert(overlay_id.clone(), overlay_info);
                }

                overlay_ids.push(overlay_id);
                println!("[OVERLAY] Created screenshot overlay for monitor {}: {}x{} at ({}, {})",
                    index, monitor.width().unwrap_or(1920), monitor.height().unwrap_or(1080),
                    monitor.x().unwrap_or(0), monitor.y().unwrap_or(0));
            }
            Err(e) => {
                let error_msg = format!("Failed to create overlay for monitor {}: {}", index, e);
                println!("[ERROR] {}", error_msg);

                // 清理已创建的覆盖层
                for existing_id in &overlay_ids {
                    if let Some(existing_window) = app_handle.get_webview_window(existing_id) {
                        let _ = existing_window.close();
                    }
                }

                return Err(error_msg);
            }
        }
    }

    println!("[OVERLAY] Successfully created {} screenshot overlays", overlay_ids.len());
    Ok(overlay_ids)
}

/// 关闭所有截图覆盖层
#[command]
pub async fn close_all_screenshot_overlays(app_handle: AppHandle) -> Result<(), String> {
    println!("[OVERLAY] Closing all screenshot overlays");

    let overlay_ids: Vec<String> = {
        if let Ok(manager) = OVERLAY_MANAGER.lock() {
            manager.iter()
                .filter(|(_, info)| matches!(info.window_type, OverlayType::ScreenshotOverlay))
                .map(|(id, _)| id.clone())
                .collect()
        } else {
            return Err("Failed to access overlay manager".to_string());
        }
    };

    for overlay_id in overlay_ids {
        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            if let Err(e) = window.close() {
                println!("[WARNING] Failed to close overlay {}: {}", overlay_id, e);
            }
        }

        // 从管理器中移除
        if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
            manager.remove(&overlay_id);
        }
    }

    println!("[OVERLAY] All screenshot overlays closed");
    Ok(())
}


