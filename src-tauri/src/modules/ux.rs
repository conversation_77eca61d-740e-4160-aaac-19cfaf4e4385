// 用户体验优化模块 - P1-T7: 实现视觉提示、模式切换和统一退出机制
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};

/// 用户体验配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UXConfig {
    pub enable_visual_feedback: bool,
    pub enable_sound_feedback: bool,
    pub cursor_feedback_enabled: bool,
    pub mode_transitions_enabled: bool,
    pub escape_behavior: EscapeBehavior,
    pub animation_duration: u32, // milliseconds
}

/// Escape键行为配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EscapeBehavior {
    CloseImmediately,    // 立即关闭
    StepBack,           // 逐步退出
    ConfirmBeforeClose, // 确认后关闭
}

/// 用户交互模式
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum InteractionMode {
    Select,                 // 选择模式
    Drag,                   // 拖拽模式
    Resize,                 // 调整大小模式
    Ready,                  // 准备捕获模式
    Disabled,               // 禁用模式
    WindowWithAutoSwitch,   // 窗口检测模式（支持自动切换到区域选择）
}

/// 视觉反馈类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FeedbackType {
    ModeChange,
    Selection,
    Error,
    Success,
    Warning,
}

/// 反馈事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeedbackEvent {
    pub feedback_type: FeedbackType,
    pub message: String,
    pub duration: Option<u32>,
    pub data: Option<serde_json::Value>,
}

impl Default for UXConfig {
    fn default() -> Self {
        Self {
            enable_visual_feedback: true,
            enable_sound_feedback: false,
            cursor_feedback_enabled: true,
            mode_transitions_enabled: true,
            escape_behavior: EscapeBehavior::StepBack,
            animation_duration: 300,
        }
    }
}

// 全局UX状态管理
lazy_static::lazy_static! {
    static ref UX_CONFIG: Arc<Mutex<UXConfig>> = Arc::new(Mutex::new(UXConfig::default()));
    static ref ACTIVE_MODES: Arc<Mutex<HashMap<String, InteractionMode>>> = Arc::new(Mutex::new(HashMap::new()));
}

/// 获取UX配置
#[command]
pub async fn get_ux_config() -> Result<UXConfig, String> {
    let config = UX_CONFIG.lock().map_err(|e| format!("Failed to lock UX config: {}", e))?;
    Ok(config.clone())
}

/// 更新UX配置
#[command]
pub async fn update_ux_config(new_config: UXConfig) -> Result<(), String> {
    {
        let mut config = UX_CONFIG.lock().map_err(|e| format!("Failed to lock UX config: {}", e))?;
        *config = new_config.clone();
    }
    
    println!("[UX] Configuration updated: visual_feedback={}, escape_behavior={:?}", 
             new_config.enable_visual_feedback, new_config.escape_behavior);
    Ok(())
}

/// 设置窗口交互模式
#[command]
pub async fn set_interaction_mode(
    app_handle: AppHandle,
    window_id: String,
    mode: String
) -> Result<(), String> {
    let interaction_mode = match mode.as_str() {
        "select" => InteractionMode::Select,
        "drag" => InteractionMode::Drag,
        "resize" => InteractionMode::Resize,
        "ready" => InteractionMode::Ready,
        "disabled" => InteractionMode::Disabled,
        "window_with_auto_switch" => InteractionMode::WindowWithAutoSwitch,
        _ => return Err(format!("Unknown interaction mode: {}", mode)),
    };
    
    // 更新模式状态
    {
        let mut modes = ACTIVE_MODES.lock().map_err(|e| format!("Failed to lock modes: {}", e))?;
        modes.insert(window_id.clone(), interaction_mode.clone());
    }
    
    // 发送模式变化事件到前端
    if let Some(window) = app_handle.get_webview_window(&window_id) {
        let event_data = serde_json::json!({
            "mode": mode,
            "timestamp": chrono::Utc::now().timestamp_millis()
        });
        
        window.emit("mode-changed", &event_data)
            .map_err(|e| format!("Failed to emit mode change event: {}", e))?;
    }
    
    println!("[UX] Interaction mode set for window {}: {:?}", window_id, interaction_mode);
    Ok(())
}

/// 获取窗口当前交互模式
#[command]
pub async fn get_interaction_mode(window_id: String) -> Result<String, String> {
    let modes = ACTIVE_MODES.lock().map_err(|e| format!("Failed to lock modes: {}", e))?;
    
    let mode = modes.get(&window_id).unwrap_or(&InteractionMode::Select);
    let mode_str = match mode {
        InteractionMode::Select => "select",
        InteractionMode::Drag => "drag",
        InteractionMode::Resize => "resize",
        InteractionMode::Ready => "ready",
        InteractionMode::Disabled => "disabled",
        InteractionMode::WindowWithAutoSwitch => "window_with_auto_switch",
    };
    
    Ok(mode_str.to_string())
}

/// 发送用户反馈
#[command]
pub async fn send_user_feedback(
    app_handle: AppHandle,
    window_id: String,
    feedback: FeedbackEvent
) -> Result<(), String> {
    let config = UX_CONFIG.lock().map_err(|e| format!("Failed to lock UX config: {}", e))?;
    
    if !config.enable_visual_feedback {
        return Ok(());
    }
    
    if let Some(window) = app_handle.get_webview_window(&window_id) {
        window.emit("user-feedback", &feedback)
            .map_err(|e| format!("Failed to emit feedback event: {}", e))?;
    }
    
    println!("[UX] Feedback sent to {}: {:?} - {}", window_id, feedback.feedback_type, feedback.message);
    Ok(())
}

/// 处理Escape键事件
#[command]
pub async fn handle_escape_key(
    app_handle: AppHandle,
    window_id: String
) -> Result<String, String> {
    println!("[UX] ESC key event received for window: {}", window_id);
    let escape_behavior = {
        let config = UX_CONFIG.lock().map_err(|e| format!("Failed to lock UX config: {}", e))?;
        config.escape_behavior.clone()
    };
    
    let current_mode = get_interaction_mode(window_id.clone()).await?;
    println!("[UX] Current interaction mode: {}", current_mode);
    
    // 根据设计文档要求：ESC键应该总是退出截图功能，不进行模式切换
    let action = match escape_behavior {
        EscapeBehavior::CloseImmediately => "close",
        EscapeBehavior::StepBack => {
            // 对于窗口检测模式和区域选择模式，ESC都应该直接退出
            match current_mode.as_str() {
                "window_with_auto_switch" => "close", // 窗口检测模式 → 直接退出
                "select" | "drag" | "resize" | "ready" => "close", // 区域选择模式 → 直接退出
                _ => "close",
            }
        }
        EscapeBehavior::ConfirmBeforeClose => "confirm_close",
    };
    
    // 发送反馈
    let feedback = FeedbackEvent {
        feedback_type: FeedbackType::ModeChange,
        message: format!("Escape pressed: {}", action),
        duration: Some(1000),
        data: Some(serde_json::json!({
            "action": action,
            "previous_mode": current_mode
        })),
    };
    
    send_user_feedback(app_handle.clone(), window_id.clone(), feedback).await?;

    // 如果动作是关闭，执行完整的退出流程
    if action == "close" {
        println!("[UX] Executing complete exit flow for window: {}", window_id);
        exit_capture_completely(app_handle, window_id.clone()).await?;
        println!("[UX] Complete exit flow finished");
    }

    println!("[UX] Escape key handled: action={}, previous_mode={}", action, current_mode);
    Ok(action.to_string())
}

/// 切换到下一个交互模式
#[command]
pub async fn cycle_interaction_mode(
    app_handle: AppHandle,
    window_id: String
) -> Result<String, String> {
    let current_mode = get_interaction_mode(window_id.clone()).await?;
    
    let next_mode = match current_mode.as_str() {
        "select" => "ready",
        "ready" => "select",
        _ => "select",
    };
    
    set_interaction_mode(app_handle.clone(), window_id.clone(), next_mode.to_string()).await?;
    
    // 发送反馈
    let feedback = FeedbackEvent {
        feedback_type: FeedbackType::ModeChange,
        message: format!("Mode cycled: {} → {}", current_mode, next_mode),
        duration: Some(1500),
        data: Some(serde_json::json!({
            "previous_mode": current_mode,
            "new_mode": next_mode
        })),
    };
    
    send_user_feedback(app_handle, window_id, feedback).await?;
    
    Ok(next_mode.to_string())
}

/// 重置窗口UX状态
#[command]
pub async fn reset_ux_state(window_id: String) -> Result<(), String> {
    {
        let mut modes = ACTIVE_MODES.lock().map_err(|e| format!("Failed to lock modes: {}", e))?;
        modes.remove(&window_id);
    }
    
    println!("[UX] UX state reset for window: {}", window_id);
    Ok(())
}

/// 获取UX统计信息
#[command]
pub async fn get_ux_stats() -> Result<serde_json::Value, String> {
    let config = UX_CONFIG.lock().map_err(|e| format!("Failed to lock UX config: {}", e))?;
    let modes = ACTIVE_MODES.lock().map_err(|e| format!("Failed to lock modes: {}", e))?;
    
    let mode_counts = modes.values().fold(HashMap::new(), |mut acc, mode| {
        let mode_str = match mode {
            InteractionMode::Select => "select",
            InteractionMode::Drag => "drag",
            InteractionMode::Resize => "resize",
            InteractionMode::Ready => "ready",
            InteractionMode::Disabled => "disabled",
            InteractionMode::WindowWithAutoSwitch => "window_with_auto_switch",
        };
        *acc.entry(mode_str).or_insert(0) += 1;
        acc
    });
    
    let stats = serde_json::json!({
        "config": config.clone(),
        "active_windows": modes.len(),
        "mode_distribution": mode_counts,
        "timestamp": chrono::Utc::now().timestamp_millis()
    });
    
    Ok(stats)
}

/// 处理空格键按下事件 - 从窗口检测模式切换到区域选择模式
#[command]
pub async fn handle_spacebar_press(
    app_handle: AppHandle,
    window_id: String
) -> Result<(), String> {
    println!("[UX] Spacebar pressed - switching from window detection to region selection mode");

    // 检查当前模式是否为窗口检测模式
    let current_mode = get_interaction_mode(window_id.clone()).await?;
    if current_mode != "window_with_auto_switch" {
        return Ok(()); // 只在窗口检测模式下响应空格键
    }

    // 切换到区域选择模式
    switch_to_region_selection_mode(app_handle, window_id).await
}

/// 处理鼠标拖拽开始事件 - 从窗口检测模式切换到区域选择模式
#[command]
pub async fn handle_mouse_drag_start(
    app_handle: AppHandle,
    window_id: String,
    start_x: f64,
    start_y: f64
) -> Result<(), String> {
    println!("[UX] 🎯 Mouse drag started at ({}, {}) - attempting to switch from window detection to region selection mode", start_x, start_y);
    println!("[UX] 🎯 Window ID: {}", window_id);

    // 检查当前模式是否为窗口检测模式
    let current_mode = get_interaction_mode(window_id.clone()).await?;
    println!("[UX] 🎯 Current interaction mode: {}", current_mode);

    if current_mode != "window_with_auto_switch" {
        println!("[UX] 🎯 Mode is not window_with_auto_switch, ignoring drag event");
        return Ok(()); // 只在窗口检测模式下响应拖拽
    }

    println!("[UX] 🎯 Mode check passed, proceeding with mode switch");

    // 切换到区域选择模式
    match switch_to_region_selection_mode(app_handle, window_id).await {
        Ok(()) => {
            println!("[UX] 🎯 Successfully switched to region selection mode");
            Ok(())
        }
        Err(e) => {
            println!("[UX] 🎯 Failed to switch to region selection mode: {}", e);
            Err(e)
        }
    }
}

/// 切换到区域选择模式的核心逻辑 - 修改为单一overlay架构
async fn switch_to_region_selection_mode(
    app_handle: AppHandle,
    window_id: String
) -> Result<(), String> {
    println!("[UX] 🎯 Starting switch to region selection mode (single overlay architecture)");

    // 🚫 不再创建新的overlay，而是在当前overlay内切换模式
    println!("[UX] 🎯 Switching to region selection mode within current overlay: {}", window_id);

    // 1. 更新状态为区域选择模式
    println!("[UX] 🎯 Updating capture state to region selection mode");
    let new_capture_state = crate::modules::state::CaptureState {
        is_capturing: true,
        capture_mode: crate::modules::state::CaptureMode::Region,
        current_overlay_id: Some(window_id.clone()),
        selected_region: None,
        selected_window: None,
        capture_timestamp: Some(chrono::Utc::now().timestamp_millis() as u64),
    };

    crate::modules::state::update_capture_state(app_handle.clone(), new_capture_state)
        .await.map_err(|e| {
            println!("[UX] 🎯 Failed to update capture state: {}", e);
            format!("Failed to update capture state: {}", e)
        })?;

    // 2. 设置交互模式为选择模式
    println!("[UX] 🎯 Setting interaction mode to 'select' for current overlay");
    set_interaction_mode(app_handle.clone(), window_id.clone(), "select".to_string()).await?;

    // 3. 通知前端切换到区域选择模式
    if let Some(window) = app_handle.get_webview_window(&window_id) {
        println!("[UX] 🎯 Notifying frontend to switch to region selection mode");
        if let Err(e) = window.emit("mode-switch", serde_json::json!({
            "from": "window_detection",
            "to": "region_selection",
            "timestamp": chrono::Utc::now().timestamp_millis()
        })) {
            eprintln!("[UX] 🎯 WARNING: Failed to notify frontend: {}", e);
        }
    }

    println!("[UX] 🎯 Mode switch completed successfully (single overlay)");
    Ok(())
}

/// 应用视觉主题
#[command]
pub async fn apply_visual_theme(
    app_handle: AppHandle,
    window_id: String,
    theme_name: String
) -> Result<(), String> {
    let theme_data = match theme_name.as_str() {
        "dark" => serde_json::json!({
            "background": "rgba(0, 0, 0, 0.3)",
            "selection_color": "#007AFF",
            "text_color": "#FFFFFF"
        }),
        "light" => serde_json::json!({
            "background": "rgba(255, 255, 255, 0.3)",
            "selection_color": "#0066CC",
            "text_color": "#000000"
        }),
        "high_contrast" => serde_json::json!({
            "background": "rgba(0, 0, 0, 0.8)",
            "selection_color": "#FFFF00",
            "text_color": "#FFFFFF"
        }),
        _ => return Err(format!("Unknown theme: {}", theme_name)),
    };
    
    if let Some(window) = app_handle.get_webview_window(&window_id) {
        window.emit("theme-changed", &theme_data)
            .map_err(|e| format!("Failed to emit theme change event: {}", e))?;
    }
    
    println!("[UX] Visual theme applied: {} for window {}", theme_name, window_id);
    Ok(())
}

/// 完全退出截图功能 - 关闭所有覆盖层并恢复正常状态
async fn exit_capture_completely(app_handle: AppHandle, window_id: String) -> Result<(), String> {
    println!("[UX] Exiting capture functionality completely");

    // 1. 关闭所有覆盖层
    if let Err(e) = crate::modules::overlay::close_all_overlays(app_handle.clone()).await {
        eprintln!("[WARNING] Failed to close all overlays: {}", e);
    }

    // 2. 停止窗口检测
    if let Err(e) = crate::modules::window::stop_smart_window_detection().await {
        eprintln!("[WARNING] Failed to stop window detection: {}", e);
    }

    // 3. 重置UX状态
    reset_ux_state(window_id).await?;

    // 4. 结束截图会话
    if let Err(e) = crate::modules::state::complete_capture_session(app_handle.clone(), None, None).await {
        eprintln!("[WARNING] Failed to complete capture session: {}", e);
    }

    // 5. 显示主窗口（如果被隐藏）
    if let Some(main_window) = app_handle.get_webview_window("main") {
        if let Err(e) = main_window.show() {
            eprintln!("[WARNING] Failed to show main window: {}", e);
        }
        if let Err(e) = main_window.set_focus() {
            eprintln!("[WARNING] Failed to focus main window: {}", e);
        }
    }

    println!("[UX] Capture functionality exited completely");
    Ok(())
}

/// 直接退出截图功能 - 简化版本，不依赖窗口ID
#[command]
pub async fn exit_capture_completely_direct(app_handle: AppHandle) -> Result<(), String> {
    println!("[UX] 🚪 Direct exit capture functionality triggered");

    // 1. 隐藏所有覆盖层（保留预初始化覆盖层）
    println!("[UX] 🚪 Step 1: Hiding all overlays");
    match crate::modules::overlay::hide_all_overlays(app_handle.clone()).await {
        Ok(count) => println!("[UX] 🚪 Successfully hidden {} overlays", count),
        Err(e) => eprintln!("[UX] 🚪 WARNING: Failed to hide all overlays: {}", e),
    }

    // 2. 停止窗口检测
    println!("[UX] 🚪 Step 2: Stopping window detection");
    if let Err(e) = crate::modules::window::stop_smart_window_detection().await {
        eprintln!("[UX] 🚪 WARNING: Failed to stop window detection: {}", e);
    }

    // 3. 重置所有UX状态
    println!("[UX] 🚪 Step 3: Resetting all UX states");
    if let Ok(mut modes) = ACTIVE_MODES.lock() {
        modes.clear();
        println!("[UX] 🚪 Cleared all interaction modes");
    }

    // 4. 结束截图会话
    println!("[UX] 🚪 Step 4: Completing capture session");
    if let Err(e) = crate::modules::state::complete_capture_session(app_handle.clone(), None, None).await {
        eprintln!("[UX] 🚪 WARNING: Failed to complete capture session: {}", e);
    }

    // 5. 注销全局ESC快捷键
    println!("[UX] 🚪 Step 5: Unregistering global ESC shortcut");
    if let Err(e) = crate::modules::global_shortcuts::unregister_global_esc_shortcut(&app_handle) {
        eprintln!("[UX] 🚪 WARNING: Failed to unregister global ESC shortcut: {}", e);
    }

    // 6. 显示主窗口（如果被隐藏）
    println!("[UX] 🚪 Step 6: Showing main window");
    if let Some(main_window) = app_handle.get_webview_window("main") {
        if let Err(e) = main_window.show() {
            eprintln!("[UX] 🚪 WARNING: Failed to show main window: {}", e);
        }
        if let Err(e) = main_window.set_focus() {
            eprintln!("[UX] 🚪 WARNING: Failed to focus main window: {}", e);
        }
    }

    println!("[UX] 🚪 Direct exit capture functionality completed successfully");
    Ok(())
}
