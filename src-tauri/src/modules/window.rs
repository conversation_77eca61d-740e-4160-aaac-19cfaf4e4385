// 窗口管理模块 - 专门处理窗口检测和管理功能
use serde::{Deserialize, Serialize};
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tauri::{command, A<PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};
use xcap::Window;

// 全局窗口检测控制标志
static WINDOW_DETECTION_ACTIVE: AtomicBool = AtomicBool::new(false);

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowInfo {
    pub id: u64,
    pub title: String,
    pub app_name: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub is_minimized: bool,
    pub is_visible: bool,
}

#[derive(Debug, Clone)]
struct WindowListCache {
    windows: Vec<WindowInfo>,
    last_updated: Instant,
    ttl: Duration,
}

impl WindowListCache {
    fn new() -> Self {
        Self {
            windows: Vec::new(),
            last_updated: Instant::now() - Duration::from_secs(60),
            ttl: Duration::from_secs(2),
        }
    }

    fn is_expired(&self) -> bool {
        self.last_updated.elapsed() > self.ttl
    }

    fn update(&mut self, windows: Vec<WindowInfo>) {
        self.windows = windows;
        self.last_updated = Instant::now();
    }
}

// 全局窗口缓存
lazy_static::lazy_static! {
    static ref WINDOW_CACHE: Arc<Mutex<WindowListCache>> = Arc::new(Mutex::new(WindowListCache::new()));
}

/// 获取所有窗口列表（带缓存）
#[command]
pub async fn list_windows_new() -> Result<Vec<WindowInfo>, String> {
    // 检查缓存
    if let Ok(cache) = WINDOW_CACHE.lock() {
        if !cache.is_expired() {
            println!(
                "[WINDOW] Returning cached window list ({} windows)",
                cache.windows.len()
            );
            return Ok(cache.windows.clone());
        }
    }

    // 缓存过期，重新获取
    println!("[WINDOW] Cache expired, fetching fresh window list");
    let windows = get_window_list_fresh().await?;

    // 更新缓存
    if let Ok(mut cache) = WINDOW_CACHE.lock() {
        cache.update(windows.clone());
    }

    Ok(windows)
}

/// 强制刷新窗口列表
#[command]
pub async fn refresh_window_list() -> Result<Vec<WindowInfo>, String> {
    println!("[WINDOW] Force refreshing window list");
    let windows = get_window_list_fresh().await?;

    // 更新缓存
    if let Ok(mut cache) = WINDOW_CACHE.lock() {
        cache.update(windows.clone());
    }

    Ok(windows)
}

/// 获取新鲜的窗口列表（不使用缓存）
async fn get_window_list_fresh() -> Result<Vec<WindowInfo>, String> {
    let start_time = Instant::now();

    let windows = Window::all().map_err(|e| format!("Failed to get windows: {}", e))?;

    let mut window_infos = Vec::new();

    for window in windows {
        // 获取窗口信息
        let title = window.title().unwrap_or_default();
        let app_name = window.app_name().unwrap_or_default();

        // 过滤掉系统窗口和无效窗口
        if title.is_empty()
            || title.starts_with("Window Server")
            || title.starts_with("Dock")
            || app_name.is_empty()
        {
            continue;
        }

        // 获取窗口几何信息 - xcap API更新
        let window_id = window
            .id()
            .map_err(|e| format!("Failed to get window ID: {}", e))? as u64;
        let x = window.x().unwrap_or(0);
        let y = window.y().unwrap_or(0);
        let width = window.width().unwrap_or(0);
        let height = window.height().unwrap_or(0);

        let window_info = WindowInfo {
            id: window_id,
            title,
            app_name,
            x,
            y,
            width,
            height,
            is_minimized: false, // xcap暂不支持，后续可扩展
            is_visible: true,    // xcap暂不支持，后续可扩展
        };

        window_infos.push(window_info);
    }

    let _elapsed = start_time.elapsed();
    // println!("[WINDOW] Window list fetched: {} windows in {:.2}ms",
    //     window_infos.len(), _elapsed.as_millis());

    Ok(window_infos)
}

/// 根据ID获取窗口信息
#[command]
pub async fn get_window_info_new(window_id: u64) -> Result<Option<WindowInfo>, String> {
    let windows = list_windows_new().await?;
    Ok(windows.into_iter().find(|w| w.id == window_id))
}

/// 验证窗口是否存在
#[command]
pub async fn validate_window_exists_new(window_id: u64) -> Result<bool, String> {
    let window_info = get_window_info_new(window_id).await?;
    Ok(window_info.is_some())
}

/// 清除窗口缓存
#[command]
pub async fn clear_window_cache_new() -> Result<(), String> {
    if let Ok(mut cache) = WINDOW_CACHE.lock() {
        cache.windows.clear();
        cache.last_updated = Instant::now() - Duration::from_secs(60); // 强制过期
        println!("[WINDOW] Window cache cleared");
    }
    Ok(())
}

/// 实时鼠标位置窗口检测
#[command]
pub async fn detect_window_under_mouse(x: i32, y: i32) -> Result<Option<WindowInfo>, String> {
    let start_time = Instant::now();
    println!(
        "[WINDOW_DETECT] Starting detection at coordinates: ({}, {})",
        x, y
    );

    let windows = list_windows_new().await?;
    println!(
        "[WINDOW_DETECT] Loaded {} windows for detection",
        windows.len()
    );

    // 查找鼠标位置下的窗口 - 按Z-order排序（最上层优先）
    let mut detected_windows = Vec::new();

    for window in &windows {
        if x >= window.x
            && x <= window.x + window.width as i32
            && y >= window.y
            && y <= window.y + window.height as i32
        {
            println!(
                "[WINDOW_DETECT] Window '{}' contains point ({}, {}) - bounds: ({}, {}, {}, {})",
                window.title, x, y, window.x, window.y, window.width, window.height
            );
            detected_windows.push(window.clone());
        }
    }

    let elapsed = start_time.elapsed();

    if detected_windows.is_empty() {
        println!(
            "[WINDOW_DETECT] No windows detected at ({}, {}) in {:.2}ms",
            x,
            y,
            elapsed.as_millis()
        );
        Ok(None)
    } else {
        // 返回第一个检测到的窗口（通常是最上层的）
        let selected_window = &detected_windows[0];
        println!(
            "[WINDOW_DETECT] Selected window: '{}' (ID: {}) from {} candidates in {:.2}ms",
            selected_window.title,
            selected_window.id,
            detected_windows.len(),
            elapsed.as_millis()
        );
        Ok(Some(selected_window.clone()))
    }
}

/// 实时窗口检测（专为浮层优化）
#[command]
pub async fn detect_window_under_mouse_realtime(
    x: i32,
    y: i32,
    exclude_overlay_windows: bool,
) -> Result<Option<WindowInfo>, String> {
    let start_time = Instant::now();
    println!(
        "[WINDOW_REALTIME] Real-time detection at ({}, {}) - exclude_overlay: {}",
        x, y, exclude_overlay_windows
    );

    // 获取新鲜的窗口列表（不使用缓存以确保实时性）
    let windows = get_window_list_fresh().await?;
    println!(
        "[WINDOW_REALTIME] Fresh window list loaded: {} windows",
        windows.len()
    );

    // 过滤掉浮层窗口和系统窗口
    let filtered_windows: Vec<WindowInfo> = windows
        .into_iter()
        .filter(|window| {
            let is_overlay = window.title.contains("Mecap")
                || window.title.contains("Overlay")
                || window.app_name.contains("Mecap");

            let is_system = is_system_window(&window.title, &window.app_name);

            let should_include = if exclude_overlay_windows {
                !is_overlay && !is_system
            } else {
                !is_system
            };

            if !should_include {
                println!(
                    "[WINDOW_REALTIME] Filtered out window: '{}' (overlay: {}, system: {})",
                    window.title, is_overlay, is_system
                );
            }

            should_include
        })
        .collect();

    println!(
        "[WINDOW_REALTIME] After filtering: {} windows remain",
        filtered_windows.len()
    );

    // 查找鼠标位置下的窗口
    for window in &filtered_windows {
        if x >= window.x
            && x <= window.x + window.width as i32
            && y >= window.y
            && y <= window.y + window.height as i32
        {
            let elapsed = start_time.elapsed();
            println!(
                "[WINDOW_REALTIME] ✅ Detected window: '{}' ({}x{} at {},{}) in {:.2}ms",
                window.title,
                window.width,
                window.height,
                window.x,
                window.y,
                elapsed.as_millis()
            );
            return Ok(Some(window.clone()));
        }
    }

    let elapsed = start_time.elapsed();
    println!(
        "[WINDOW_REALTIME] ❌ No window detected at ({}, {}) in {:.2}ms",
        x,
        y,
        elapsed.as_millis()
    );
    Ok(None)
}

/// 窗口检测性能测试
#[command]
pub async fn benchmark_window_detection() -> Result<String, String> {
    println!("[WINDOW] Starting window detection performance benchmark");

    let mut results = Vec::new();
    results.push("=== Window Detection Performance Benchmark ===".to_string());

    // 测试窗口列表获取性能
    let mut times = Vec::new();
    for i in 0..5 {
        let start = Instant::now();
        let windows = get_window_list_fresh().await?;
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);

        results.push(format!(
            "Test {}: {} windows in {}ms",
            i + 1,
            windows.len(),
            elapsed
        ));

        // 短暂延迟
        tokio::time::sleep(Duration::from_millis(100)).await;
    }

    // 计算统计数据
    let avg_time = times.iter().sum::<u128>() / times.len() as u128;
    let min_time = times.iter().min().unwrap_or(&0);
    let max_time = times.iter().max().unwrap_or(&0);

    results.push(format!("\nPerformance Summary:"));
    results.push(format!("- Average: {}ms", avg_time));
    results.push(format!("- Range: {}-{}ms", min_time, max_time));

    // 性能评估
    match avg_time {
        0..=50 => results.push("✅ Excellent performance".to_string()),
        51..=100 => results.push("✅ Good performance".to_string()),
        101..=200 => results.push("⚠️ Acceptable performance".to_string()),
        _ => results.push("❌ Poor performance - optimization needed".to_string()),
    }

    let report = results.join("\n");
    println!("[WINDOW] Benchmark completed:\n{}", report);
    Ok(report)
}

/// 智能窗口检测系统 - Phase 1核心功能
#[command]
pub async fn start_smart_window_detection(app_handle: AppHandle) -> Result<String, String> {
    println!("[WINDOW] Starting smart window detection system");

    // 设置检测为活跃状态
    WINDOW_DETECTION_ACTIVE.store(true, std::sync::atomic::Ordering::Relaxed);

    // 启动实时窗口检测
    let app_handle_clone = app_handle.clone();
    tauri::async_runtime::spawn(async move {
        let mut detection_interval = Duration::from_millis(500); // 初始检测间隔
        let mut last_window_count = 0;
        let mut stable_count = 0;

        while WINDOW_DETECTION_ACTIVE.load(std::sync::atomic::Ordering::Relaxed) {
            // 获取当前窗口列表
            match get_window_list_fresh().await {
                Ok(windows) => {
                    let filtered_windows = filter_relevant_windows(windows);
                    let current_count = filtered_windows.len();

                    // 检测窗口变化
                    if current_count == last_window_count {
                        stable_count += 1;

                        // 如果窗口列表稳定，增加检测间隔以节省资源
                        if stable_count > 10 {
                            detection_interval = Duration::from_millis(2000);
                        } else if stable_count > 5 {
                            detection_interval = Duration::from_millis(1000);
                        }
                    } else {
                        // 窗口有变化，重置计数器并减少检测间隔
                        stable_count = 0;
                        detection_interval = Duration::from_millis(200);
                        last_window_count = current_count;

                        // 发送窗口变化事件
                        if let Some(main_window) = app_handle_clone.get_webview_window("main") {
                            let _ = main_window.emit("window-list-changed", &filtered_windows);
                        }

                        println!(
                            "[WINDOW] Window list changed: {} windows detected",
                            current_count
                        );
                    }
                }
                Err(e) => {
                    eprintln!("[ERROR] Failed to get window list: {}", e);
                    detection_interval = Duration::from_millis(1000); // 错误时降低频率
                }
            }

            tokio::time::sleep(detection_interval).await;
        }

        println!("[WINDOW] Smart window detection stopped");
    });

    Ok("Smart window detection started".to_string())
}

/// 停止智能窗口检测
#[command]
pub async fn stop_smart_window_detection() -> Result<(), String> {
    println!("[WINDOW] Smart window detection stop requested");

    // 设置检测为非活跃状态，这将停止后台检测循环
    WINDOW_DETECTION_ACTIVE.store(false, std::sync::atomic::Ordering::Relaxed);

    println!("[WINDOW] Smart window detection flag set to false");
    Ok(())
}

/// 实时窗口检测 - 检测鼠标下方的窗口
#[command]
pub async fn detect_window_under_cursor(x: i32, y: i32) -> Result<Option<WindowInfo>, String> {
    println!("[WINDOW] 🎯 Detecting window at coordinates ({}, {})", x, y);

    // 使用混合截图模块的智能检测功能
    match crate::modules::hybrid_screenshot::detect_window_smart(x, y).await {
        Ok(Some(hybrid_window_info)) => {
            println!(
                "[WINDOW] 🎯 Found window: {:?} ({:?})",
                hybrid_window_info.title, hybrid_window_info.process_name
            );

            // 转换为window模块的WindowInfo格式
            let window_info = WindowInfo {
                id: hybrid_window_info.handle,
                title: hybrid_window_info
                    .title
                    .unwrap_or_else(|| "Untitled".to_string()),
                app_name: hybrid_window_info
                    .process_name
                    .unwrap_or_else(|| "Unknown".to_string()),
                x: hybrid_window_info.x,
                y: hybrid_window_info.y,
                width: hybrid_window_info.width,
                height: hybrid_window_info.height,
                is_minimized: false, // TODO: 从混合截图模块获取
                is_visible: true,    // TODO: 从混合截图模块获取
            };

            Ok(Some(window_info))
        }
        Ok(None) => {
            println!("[WINDOW] 🎯 No window found at coordinates");
            Ok(None)
        }
        Err(e) => {
            println!("[WINDOW] 🎯 Error detecting window: {}", e);
            Err(format!("Window detection failed: {}", e))
        }
    }
}

/// 获取窗口图标和详细信息
#[command]
pub async fn get_window_details(window_id: u64) -> Result<WindowDetails, String> {
    println!("[WINDOW] 🔍 Getting details for window ID: {}", window_id);

    // 获取窗口列表并找到对应窗口
    match get_window_list_fresh().await {
        Ok(windows) => {
            if let Some(window) = windows.into_iter().find(|w| w.id == window_id) {
                let details = WindowDetails {
                    id: window.id,
                    title: window.title.clone(),
                    app_name: window.app_name.clone(),
                    icon_path: None, // TODO: 实现图标获取
                    bounds: WindowBounds {
                        x: window.x,
                        y: window.y,
                        width: window.width,
                        height: window.height,
                    },
                };
                Ok(details)
            } else {
                Err(format!("Window with ID {} not found", window_id))
            }
        }
        Err(e) => Err(format!("Failed to get window list: {}", e)),
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowDetails {
    pub id: u64,
    pub title: String,
    pub app_name: String,
    pub icon_path: Option<String>,
    pub bounds: WindowBounds,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowBounds {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

/// 捕获选中的窗口
#[command]
pub async fn capture_selected_window(
    app_handle: AppHandle,
    window_id: u64,
) -> Result<String, String> {
    println!("[WINDOW] 📸 Capturing selected window ID: {}", window_id);

    // 获取窗口详细信息
    match get_window_details(window_id).await {
        Ok(window_details) => {
            println!(
                "[WINDOW] 📸 Window details: {} ({}x{})",
                window_details.title, window_details.bounds.width, window_details.bounds.height
            );

            // 使用窗口边界进行区域截图
            let area = crate::modules::capture::ScreenshotArea {
                x: window_details.bounds.x as f64,
                y: window_details.bounds.y as f64,
                width: window_details.bounds.width as f64,
                height: window_details.bounds.height as f64,
            };

            // 调用区域截图功能
            match crate::modules::capture::capture_region_new(area).await {
                Ok(_result) => {
                    println!("[WINDOW] 📸 Window captured successfully");

                    // 隐藏所有覆盖层
                    if let Err(e) = crate::modules::overlay::hide_all_overlays(app_handle).await {
                        eprintln!("[WINDOW] 📸 WARNING: Failed to hide overlays: {}", e);
                    }

                    Ok(format!(
                        "Window captured: {}x{}",
                        window_details.bounds.width, window_details.bounds.height
                    ))
                }
                Err(e) => Err(format!("Failed to capture window: {}", e)),
            }
        }
        Err(e) => Err(format!("Failed to get window details: {}", e)),
    }
}

/// 智能窗口过滤 - 过滤掉不相关的系统窗口
pub fn filter_relevant_windows(windows: Vec<WindowInfo>) -> Vec<WindowInfo> {
    windows
        .into_iter()
        .filter(|window| {
            // 过滤条件
            !window.title.is_empty() &&
        !window.app_name.is_empty() &&
        window.width > 50 &&  // 最小宽度
        window.height > 50 && // 最小高度
        !is_system_window(&window.title, &window.app_name)
        })
        .collect()
}

/// 判断是否为系统窗口
fn is_system_window(title: &str, app_name: &str) -> bool {
    let system_titles = [
        "Window Server",
        "Dock",
        "Desktop",
        "Wallpaper",
        "Control Center",
        "Notification Center",
        "Mecap",
    ];

    let system_apps = ["WindowServer", "Dock", "Finder", "SystemUIServer", "mecap"];

    system_titles
        .iter()
        .any(|&sys_title| title.contains(sys_title))
        || system_apps
            .iter()
            .any(|&sys_app| app_name.contains(sys_app))
}

/// 窗口检测性能优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowDetectionConfig {
    pub enable_smart_detection: bool,
    pub detection_interval_ms: u64,
    pub enable_mouse_tracking: bool,
    pub enable_caching: bool,
    pub cache_ttl_seconds: u64,
}

impl Default for WindowDetectionConfig {
    fn default() -> Self {
        Self {
            enable_smart_detection: true,
            detection_interval_ms: 500,
            enable_mouse_tracking: true,
            enable_caching: true,
            cache_ttl_seconds: 2,
        }
    }
}

/// 获取窗口检测配置
#[command]
pub async fn get_window_detection_config() -> Result<WindowDetectionConfig, String> {
    Ok(WindowDetectionConfig::default())
}

/// 更新窗口检测配置
#[command]
pub async fn update_window_detection_config(config: WindowDetectionConfig) -> Result<(), String> {
    println!("[WINDOW] Updating window detection config: {:?}", config);

    // 更新缓存TTL
    if let Ok(mut cache) = WINDOW_CACHE.lock() {
        cache.ttl = Duration::from_secs(config.cache_ttl_seconds);
    }

    println!("[WINDOW] Window detection config updated");
    Ok(())
}
