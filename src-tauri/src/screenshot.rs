use std::time::SystemTime;
use image::{ImageFormat, GenericImageView};
use thiserror::Error;
use tauri::command;
use xcap::Monitor;

#[derive(Error, Debug)]
pub enum CaptureError {
    #[error("Screen capture failed: {0}")]
    CaptureFailed(String),
    #[error("Image processing failed: {0}")]
    ImageError(#[from] image::ImageError),
    #[error("Filesystem error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Unsupported platform")]
    UnsupportedPlatform,
}

#[command]
pub async fn capture_screenshot(region: Option<(u32, u32, u32, u32)>) -> Result<String, String> {
    let screens = Monitor::all().map_err(|e| CaptureError::CaptureFailed(e.to_string()).to_string())?;
    
    if screens.is_empty() {
        return Err(CaptureError::CaptureFailed("No screens found".to_string()).to_string());
    }

    // Use first available screen (xcap doesn't have is_primary field)
    let screen = screens.first()
        .ok_or_else(|| CaptureError::CaptureFailed("No screen available".to_string()).to_string())?;

    let image = screen.capture_image()
        .map_err(|e| CaptureError::CaptureFailed(e.to_string()).to_string())?;

    // If region is specified, crop the image
    let final_image = match region {
        Some((x, y, width, height)) => {
            // Crop the image to the specified region
            image.view(x, y, width, height).to_image()
        }
        None => image
    };

    // Create output directory
    let mut path = dirs::picture_dir()
        .ok_or_else(|| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::NotFound, 
            "Pictures directory not found"
        )).to_string())?;
    
    path.push("screenshots");
    std::fs::create_dir_all(&path)
        .map_err(|e| CaptureError::IoError(e).to_string())?;

    // Generate filename
    let timestamp = SystemTime::now()
        .duration_since(SystemTime::UNIX_EPOCH)
        .map_err(|e| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::Other, 
            e.to_string()
        )).to_string())?
        .as_secs();
    
    let filename = format!("screenshot_{}.png", timestamp);
    path.push(filename);

    // Save as PNG with optimized encoding
    final_image.save_with_format(&path, ImageFormat::Png)
        .map_err(|e| CaptureError::ImageError(e).to_string())?;

    path.to_str()
        .map(|s| s.to_string())
        .ok_or_else(|| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::InvalidData,
            "Invalid path encoding"
        )).to_string())
}