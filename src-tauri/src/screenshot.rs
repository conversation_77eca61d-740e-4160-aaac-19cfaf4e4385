use std::path::PathBuf;
use std::time::SystemTime;
use image::ImageFormat;
use screenshot::{Screen, ScreenCapture};
use thiserror::Error;
use tauri::command;

#[derive(Error, Debug)]
pub enum CaptureError {
    #[error("Screen capture failed: {0}")]
    CaptureFailed(String),
    #[error("Image processing failed: {0}")]
    ImageError(#[from] image::ImageError),
    #[error("Filesystem error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Unsupported platform")]
    UnsupportedPlatform,
}

#[command]
pub async fn capture_screenshot(region: Option<(u32, u32, u32, u32)>) -> Result<String, String> {
    let screens = Screen::all().map_err(|e| CaptureError::CaptureFailed(e.to_string()).to_string())?;
    
    if screens.is_empty() {
        return Err(CaptureError::CaptureFailed("No screens found".to_string()).to_string());
    }

    // Use primary screen if no region specified
    let screen = screens.iter().find(|s| s.is_primary)
        .ok_or_else(|| CaptureError::CaptureFailed("Primary screen not found".to_string()).to_string())?;

    let buffer = match region {
        Some((x, y, width, height)) => {
            screen.capture_area(x as i32, y as i32, width, height)
                .map_err(|e| CaptureError::CaptureFailed(e.to_string()).to_string())?
        }
        None => {
            screen.capture()
                .map_err(|e| CaptureError::CaptureFailed(e.to_string()).to_string())?
        }
    };

    // Create output directory
    let mut path = dirs::picture_dir()
        .ok_or_else(|| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::NotFound, 
            "Pictures directory not found"
        )).to_string())?;
    
    path.push("screenshots");
    std::fs::create_dir_all(&path)
        .map_err(|e| CaptureError::IoError(e).to_string())?;

    // Generate filename
    let timestamp = SystemTime::now()
        .duration_since(SystemTime::UNIX_EPOCH)
        .map_err(|e| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::Other, 
            e.to_string()
        )).to_string())?
        .as_secs();
    
    let filename = format!("screenshot_{}.png", timestamp);
    path.push(filename);

    // Save as PNG with optimized encoding
    image::load_from_memory(&buffer)
        .map_err(|e| CaptureError::ImageError(e).to_string())?
        .save_with_format(&path, ImageFormat::Png)
        .map_err(|e| CaptureError::ImageError(e).to_string())?;

    path.to_str()
        .map(|s| s.to_string())
        .ok_or_else(|| CaptureError::IoError(std::io::Error::new(
            std::io::ErrorKind::InvalidData,
            "Invalid path encoding"
        )).to_string())
}