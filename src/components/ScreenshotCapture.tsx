import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Snackbar,
  Alert
} from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
// import { sendNotification, isPermissionGranted, requestPermission } from '@tauri-apps/plugin-notification';
import { useScreenshotStore } from '../store/screenshotStore';
import { ScreenshotPreview } from './ScreenshotPreview';
import { ScreenshotEditor } from './ScreenshotEditor';
import { RegionSelectionOverlay } from './RegionSelectionOverlay';
import { WindowHoverPreview, type WindowInfo } from './WindowHoverPreview';
import { useRegionSelection } from '../hooks/useRegionSelection';
import { useWindowHover } from '../hooks/useWindowHover';
import { convertFileSrc } from '@tauri-apps/api/core';

interface ScreenshotResult {
  path: string;
  width: number;
  height: number;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ScreenshotPreviewData {
  temp_path: string;
  width: number;
  height: number;
  suggested_filename: string;
}

// 导出截图功能
export const captureScreenshot = async () => {
  try {
    const result = await invoke<ScreenshotResult>('capture_window');
    return result.path;
  } catch (error) {
    console.error('截图失败:', error);
    return null;
  }
};

// 导出取消截图功能
export const cancelScreenshot = () => {
  console.log('截图已取消');
};

export const ScreenshotCapture = () => {
  const { addScreenshot, setIsCapturing } = useScreenshotStore();
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [editorOpen, setEditorOpen] = React.useState(false);

  // 区域选择相关状态
  const {
    isSelecting,
    backgroundImage,
    completeRegionSelection,
    cancelRegionSelection,
  } = useRegionSelection();

  // 窗口悬停相关状态
  const {
    isHoverMode,
    stopHoverMode,
    selectWindow,
  } = useWindowHover();
  const [previewData, setPreviewData] = React.useState<ScreenshotPreviewData | null>(null);
  const [captureType, setCaptureType] = React.useState<'fullscreen' | 'window'>('fullscreen');
  const [notification, setNotification] = React.useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  const showNotification = async (message: string, severity: 'success' | 'error' | 'info' = 'info') => {
    setNotification({ open: true, message, severity });

    // System notification temporarily disabled due to permission issues
    // TODO: Re-enable when overlay window permissions are properly configured
    console.log('Notification:', message);
  };





  const handlePreviewSave = async (customFilename?: string, cropArea?: CropArea) => {
    if (!previewData) return;

    try {
      setIsCapturing(true);

      // Generate final path with custom filename if provided
      let finalPath = null;
      if (customFilename) {
        const picturesDir = await invoke<string>('get_pictures_dir').catch(() => '');
        if (picturesDir) {
          finalPath = `${picturesDir}/Mecap/${customFilename}`;
        }
      }

      const result = await invoke<ScreenshotResult>('save_screenshot_from_preview', {
        tempPath: previewData.temp_path,
        finalPath,
        cropArea
      });

      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: [captureType],
        name: customFilename || `${captureType} Screenshot ${new Date().toLocaleTimeString()}`
      });

      await showNotification('Screenshot saved successfully!', 'success');
      setPreviewOpen(false);
      setPreviewData(null);

    } catch (error) {
      console.error('Save failed:', error);
      await showNotification(`Save failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  const handlePreviewEdit = () => {
    // TODO: Implement quick crop functionality
    showNotification('Quick crop functionality coming soon!', 'info');
  };

  const handleAdvancedEdit = () => {
    setPreviewOpen(false);
    setEditorOpen(true);
  };

  const handleEditorSave = async (editedImagePath: string) => {
    try {
      // 保存编辑后的图像
      const screenshot = {
        path: editedImagePath,
        tags: [],
        name: previewData?.suggested_filename || `screenshot_${Date.now()}.png`
      };

      addScreenshot(screenshot);
      setEditorOpen(false);
      setPreviewData(null);
      await showNotification('编辑后的截图已保存!', 'success');
    } catch (error) {
      console.error('Failed to save edited screenshot:', error);
      await showNotification(`保存失败: ${error}`, 'error');
    }
  };

  const handleEditorCancel = () => {
    setEditorOpen(false);
    setPreviewOpen(true);
  };

  const captureFullScreen = async () => {
    setIsCapturing(true);
    setCaptureType('fullscreen');
    try {
      // Check permission first
      const hasPermission = await invoke<boolean>('check_screen_recording_permission');
      if (!hasPermission) {
        await showNotification('需要屏幕录制权限才能进行截图', 'error');
        const message = await invoke<string>('request_screen_recording_permission');
        await showNotification(message, 'info');
        return;
      }

      const result = await invoke<ScreenshotPreviewData>('capture_fullscreen_preview');
      setPreviewData(result);
      setPreviewOpen(true);
      await showNotification('截图已捕获！请预览并保存。', 'info');
    } catch (error) {
      console.error('Full screen screenshot failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await showNotification(`全屏截图失败: ${errorMessage}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };



  // 完成区域选择
  const handleRegionSelected = async (area: { x: number; y: number; width: number; height: number }) => {
    try {
      const result = await completeRegionSelection(area) as any;
      if (result) {
        // 转换为预览数据格式
        const previewData: ScreenshotPreviewData = {
          temp_path: result.path,
          width: result.width,
          height: result.height,
          suggested_filename: `region_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.png`
        };

        setPreviewData(previewData);
        setPreviewOpen(true);
        await showNotification('区域截图已捕获！请预览并保存。', 'success');
      }
    } catch (error) {
      console.error('Region selection failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await showNotification(`区域截图失败: ${errorMessage}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  // 取消区域选择
  const handleRegionCancel = () => {
    cancelRegionSelection();
    setIsCapturing(false);
  };

  // 处理窗口选择
  const handleWindowSelected = async (window: WindowInfo) => {
    stopHoverMode(); // 停止悬停模式
    try {
      setIsCapturing(true);
      setCaptureType('window');

      // 直接捕获选中的窗口
      const result = await invoke<ScreenshotResult>('capture_window', { windowId: window.id });

      // 转换为预览数据格式
      const previewData: ScreenshotPreviewData = {
        temp_path: result.path,
        width: result.width,
        height: result.height,
        suggested_filename: `window_${window.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.png`
      };

      setPreviewData(previewData);
      setPreviewOpen(true);
      await showNotification(`窗口"${window.title}"截图已捕获！`, 'success');
    } catch (error) {
      console.error('Window capture failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await showNotification(`窗口截图失败: ${errorMessage}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  // 处理窗口保存
  const handleWindowSave = async () => {
    if (previewData) {
      await handlePreviewSave();
    }
  };

  // 处理窗口取消
  const handleWindowCancel = () => {
    stopHoverMode();
    setIsCapturing(false);
  };


  return (
    <>
    <Card sx={{ maxWidth: 400, margin: 'auto' }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          Mecap Screenshot Tool
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Use the menu bar or keyboard shortcuts to capture screenshots
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <Box sx={{
            p: 3,
            backgroundColor: 'background.paper',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider'
          }}>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              📋 Keyboard Shortcuts
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', backgroundColor: 'grey.100', px: 1, py: 0.5, borderRadius: 1 }}>
                  Cmd/Ctrl + Shift + C
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Take Screenshot
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', backgroundColor: 'grey.100', px: 1, py: 0.5, borderRadius: 1 }}>
                  Cmd/Ctrl + Shift + R
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Capture Region
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', backgroundColor: 'grey.100', px: 1, py: 0.5, borderRadius: 1 }}>
                  Cmd/Ctrl + Shift + W
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Capture Window
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', backgroundColor: 'grey.100', px: 1, py: 0.5, borderRadius: 1 }}>
                  Cmd/Ctrl + Shift + M
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Manage Screenshots
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{
            p: 2,
            backgroundColor: 'info.light',
            borderRadius: 2,
            textAlign: 'center'
          }}>
            <Typography variant="body2" sx={{ color: 'info.contrastText' }}>
              💡 You can also access these functions from the application menu
            </Typography>
          </Box>
        </Box>


      </CardContent>
    </Card>

    

    <ScreenshotPreview
      open={previewOpen}
      onClose={() => {
        setPreviewOpen(false);
        setPreviewData(null);
      }}
      onSave={handlePreviewSave}
      onEdit={handlePreviewEdit}
      onAdvancedEdit={handleAdvancedEdit}
      imagePath={previewData ? convertFileSrc(previewData.temp_path) : ''}
      captureType={captureType}
      defaultFilename={previewData?.suggested_filename || ''}
    />

    {editorOpen && previewData && (
      <ScreenshotEditor
        imagePath={convertFileSrc(previewData.temp_path)}
        onSave={handleEditorSave}
        onCancel={handleEditorCancel}
      />
    )}

    {/* 区域选择覆盖层 */}
    {isSelecting && backgroundImage && (
      <RegionSelectionOverlay
        backgroundImage={backgroundImage}
        onRegionSelected={handleRegionSelected}
        onFullScreenCapture={captureFullScreen}
        onCancel={handleRegionCancel}
      />
    )}

    {/* 窗口悬停预览 */}
    <WindowHoverPreview
      isActive={isHoverMode}
      onWindowSelected={handleWindowSelected}
      onSave={handleWindowSave}
      onCancel={handleWindowCancel}
    />

    <Snackbar
      open={notification.open}
      autoHideDuration={4000}
      onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        severity={notification.severity}
        sx={{ width: '100%' }}
      >
        {notification.message}
      </Alert>
    </Snackbar>
  </>
  );
};
