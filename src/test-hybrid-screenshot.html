<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap 混合截图测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background: #0056CC;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .monitor-info {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .monitor-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .monitor-details {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #666;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.error {
            color: #dc3545;
        }
        
        .log-entry.success {
            color: #28a745;
        }
        
        .log-entry.info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Mecap 混合截图功能测试</h1>
        
        <div class="test-section">
            <h3>1. 显示器信息检测</h3>
            <p>测试坐标系统和多显示器支持</p>
            <button onclick="testMonitorInfo()">获取显示器信息</button>
            <button onclick="testVirtualScreen()">获取虚拟屏幕边界</button>
            <div id="monitor-status" class="status" style="display: none;"></div>
            <div id="monitor-list"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 混合截图覆盖层</h3>
            <p>测试多显示器覆盖层创建和管理</p>
            <button onclick="testCreateOverlay()">创建截图覆盖层</button>
            <button onclick="testCloseOverlay()">关闭覆盖层</button>
            <div id="overlay-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 窗口检测</h3>
            <p>测试鼠标位置窗口检测功能（包含详细日志追踪）</p>
            <button onclick="testWindowDetection()">基础窗口检测</button>
            <button onclick="testSmartWindowDetection()">智能窗口检测</button>
            <button onclick="testWindowsEnhanced()" id="windows-enhanced-btn" style="display: none;">Windows增强检测</button>
            <button onclick="testMultiplePoints()">多点检测测试</button>
            <div id="window-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 坐标系统验证</h3>
            <p>测试坐标转换和区域验证</p>
            <button onclick="testCoordinateSystem()">测试坐标转换</button>
            <button onclick="testRegionValidation()">测试区域验证</button>
            <div id="coordinate-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="log">
            <strong>测试日志:</strong>
            <div id="log-content"></div>
        </div>
    </div>

    <script type="module">
        // 导入Tauri API
        const { invoke } = window.__TAURI__.core;
        
        let logCounter = 0;
        
        function log(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContent.appendChild(entry);
            logContent.scrollTop = logContent.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }
        
        // 测试显示器信息
        window.testMonitorInfo = async function() {
            log('开始测试显示器信息检测...', 'info');
            try {
                const monitors = await invoke('get_current_monitors');
                log(`检测到 ${monitors.length} 个显示器`, 'success');
                
                const monitorList = document.getElementById('monitor-list');
                monitorList.innerHTML = '';
                
                monitors.forEach((monitor, index) => {
                    const monitorDiv = document.createElement('div');
                    monitorDiv.className = 'monitor-info';
                    monitorDiv.innerHTML = `
                        <h4>显示器 ${monitor.index + 1} ${monitor.is_primary ? '(主显示器)' : ''}</h4>
                        <div class="monitor-details">
                            名称: ${monitor.name}<br>
                            位置: (${monitor.x}, ${monitor.y})<br>
                            尺寸: ${monitor.width} × ${monitor.height}<br>
                            缩放: ${monitor.scale_factor}x
                        </div>
                    `;
                    monitorList.appendChild(monitorDiv);
                });
                
                showStatus('monitor-status', `成功检测到 ${monitors.length} 个显示器`, 'success');
            } catch (error) {
                log(`显示器信息检测失败: ${error}`, 'error');
                showStatus('monitor-status', `检测失败: ${error}`, 'error');
            }
        };
        
        // 测试虚拟屏幕边界
        window.testVirtualScreen = async function() {
            log('测试虚拟屏幕边界...', 'info');
            try {
                const bounds = await invoke('get_virtual_screen_bounds');
                log(`虚拟屏幕边界: (${bounds.x}, ${bounds.y}) ${bounds.width}×${bounds.height}`, 'success');
                showStatus('monitor-status', `虚拟屏幕: ${bounds.width}×${bounds.height}`, 'info');
            } catch (error) {
                log(`虚拟屏幕边界获取失败: ${error}`, 'error');
                showStatus('monitor-status', `获取失败: ${error}`, 'error');
            }
        };
        
        // 测试创建覆盖层
        window.testCreateOverlay = async function() {
            log('开始创建混合截图覆盖层...', 'info');
            try {
                const overlayId = await invoke('start_hybrid_screenshot');
                log(`成功创建覆盖层: ${overlayId}`, 'success');
                showStatus('overlay-status', `覆盖层已创建: ${overlayId}`, 'success');
            } catch (error) {
                log(`覆盖层创建失败: ${error}`, 'error');
                showStatus('overlay-status', `创建失败: ${error}`, 'error');
            }
        };
        
        // 测试关闭覆盖层
        window.testCloseOverlay = async function() {
            log('关闭混合截图覆盖层...', 'info');
            try {
                await invoke('close_hybrid_screenshot');
                log('覆盖层已关闭', 'success');
                showStatus('overlay-status', '覆盖层已关闭', 'success');
            } catch (error) {
                log(`覆盖层关闭失败: ${error}`, 'error');
                showStatus('overlay-status', `关闭失败: ${error}`, 'error');
            }
        };
        
        // 测试基础窗口检测
        window.testWindowDetection = async function() {
            log('测试基础窗口检测功能...', 'info');
            try {
                // 测试当前鼠标位置的窗口检测
                const windowInfo = await invoke('detect_window_under_mouse', { x: 100, y: 100 });
                if (windowInfo) {
                    log(`检测到窗口: ${windowInfo.title || '未知标题'} (${windowInfo.width}×${windowInfo.height})`, 'success');
                    showStatus('window-status', `窗口检测成功: ${windowInfo.title || '未知窗口'}`, 'success');
                } else {
                    log('指定位置没有检测到窗口', 'info');
                    showStatus('window-status', '指定位置没有窗口', 'info');
                }
            } catch (error) {
                log(`窗口检测失败: ${error}`, 'error');
                showStatus('window-status', `检测失败: ${error}`, 'error');
            }
        };

        // 测试智能窗口检测
        window.testSmartWindowDetection = async function() {
            log('测试智能窗口检测功能...', 'info');
            try {
                const windowInfo = await invoke('detect_window_smart', { x: 200, y: 200 });
                if (windowInfo) {
                    log(`智能检测到窗口: ${windowInfo.title || '未知标题'} 进程: ${windowInfo.process_name || '未知'}`, 'success');
                    showStatus('window-status', `智能检测成功: ${windowInfo.title || '未知窗口'}`, 'success');
                } else {
                    log('智能检测：指定位置没有有效窗口（可能是系统窗口）', 'info');
                    showStatus('window-status', '智能检测：无有效窗口', 'info');
                }
            } catch (error) {
                log(`智能窗口检测失败: ${error}`, 'error');
                showStatus('window-status', `智能检测失败: ${error}`, 'error');
            }
        };

        // 测试Windows增强检测
        window.testWindowsEnhanced = async function() {
            log('测试Windows增强窗口检测...', 'info');
            try {
                const enhancedInfo = await invoke('detect_window_enhanced_windows', { x: 300, y: 300 });
                if (enhancedInfo) {
                    const info = enhancedInfo.basic;
                    log(`增强检测: ${info.title || '未知'} | 进程: ${info.process_name || '未知'} | 系统窗口: ${enhancedInfo.is_system} | 最大化: ${enhancedInfo.is_maximized} | Z序: ${enhancedInfo.z_order}`, 'success');
                    showStatus('window-status', `Windows增强检测成功`, 'success');
                } else {
                    log('Windows增强检测：没有检测到窗口', 'info');
                    showStatus('window-status', 'Windows增强检测：无窗口', 'info');
                }
            } catch (error) {
                log(`Windows增强检测失败: ${error}`, 'error');
                showStatus('window-status', `增强检测失败: ${error}`, 'error');
            }
        };

        // 测试多点检测
        window.testMultiplePoints = async function() {
            log('开始多点窗口检测测试...', 'info');
            const testPoints = [
                { x: 100, y: 100, desc: '左上角' },
                { x: 500, y: 300, desc: '中央' },
                { x: 800, y: 200, desc: '右侧' },
                { x: 50, y: 50, desc: '边缘' }
            ];

            for (const point of testPoints) {
                log(`测试点 ${point.desc} (${point.x}, ${point.y})...`, 'info');
                try {
                    const smartResult = await invoke('detect_window_smart', { x: point.x, y: point.y });
                    if (smartResult) {
                        log(`  智能检测成功: ${smartResult.title || '无标题'} [${smartResult.process_name || '未知进程'}]`, 'success');
                    } else {
                        log(`  智能检测: 无有效窗口`, 'info');
                    }

                    // 如果是Windows平台，也测试增强检测
                    if (navigator.platform.toLowerCase().includes('win')) {
                        const enhancedResult = await invoke('detect_window_enhanced_windows', { x: point.x, y: point.y });
                        if (enhancedResult) {
                            log(`  增强检测: 系统窗口=${enhancedResult.is_system}, Z序=${enhancedResult.z_order}`, 'info');
                        }
                    }
                } catch (error) {
                    log(`  检测失败: ${error}`, 'error');
                }

                // 添加小延迟避免过快调用
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            showStatus('window-status', '多点检测测试完成', 'success');
        };
        
        // 测试坐标系统
        window.testCoordinateSystem = async function() {
            log('测试坐标系统转换...', 'info');
            try {
                const monitor = await invoke('get_monitor_at_point', { x: 100, y: 100 });
                if (monitor) {
                    log(`坐标 (100, 100) 位于显示器 ${monitor.index + 1}`, 'success');
                    showStatus('coordinate-status', `坐标转换成功`, 'success');
                } else {
                    log('坐标 (100, 100) 不在任何显示器范围内', 'info');
                    showStatus('coordinate-status', '坐标超出范围', 'info');
                }
            } catch (error) {
                log(`坐标系统测试失败: ${error}`, 'error');
                showStatus('coordinate-status', `测试失败: ${error}`, 'error');
            }
        };
        
        // 测试区域验证
        window.testRegionValidation = async function() {
            log('测试区域验证...', 'info');
            try {
                const testRegion = {
                    x: 100,
                    y: 100,
                    width: 200,
                    height: 150,
                    monitor_index: 0
                };
                
                const isValid = await invoke('validate_screen_region', { region: testRegion });
                if (isValid) {
                    log('测试区域验证通过', 'success');
                    showStatus('coordinate-status', '区域验证通过', 'success');
                } else {
                    log('测试区域验证失败', 'error');
                    showStatus('coordinate-status', '区域验证失败', 'error');
                }
            } catch (error) {
                log(`区域验证测试失败: ${error}`, 'error');
                showStatus('coordinate-status', `验证失败: ${error}`, 'error');
            }
        };
        
        // 页面加载时自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            log('Mecap 混合截图功能测试页面已加载', 'info');
            log('点击按钮开始测试各项功能...', 'info');

            // 检测平台并显示相应按钮
            if (navigator.platform.toLowerCase().includes('win')) {
                document.getElementById('windows-enhanced-btn').style.display = 'inline-block';
                log('检测到Windows平台，启用增强窗口检测功能', 'info');
            }
        });
    </script>
</body>
</html>
